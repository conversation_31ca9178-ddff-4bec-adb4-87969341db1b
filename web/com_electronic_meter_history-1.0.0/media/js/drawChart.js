
var app = new Vue({
    el: '#hourly_price_tbody',
    data: {
        message: 'test',  
        unitPrice: 1,
        hourlyData: [],
        dailyData:[],
        monthlyData:[],
        yearlyData:[],
        show_table:false,
        annual: [],
        year:[],
        month:[],
        date_of_month:[],        
        summer_peak:2.32,
        summer_mid:1.42,
        summer_off_peak:0.91,
        not_summer_peak: 2.24,
        not_summer_mid:1.35,
        not_summer_off_peak:0.84,
        pricing: null,
        device_name:'',
        selected_date:''
    },    
    computed: {
        maxLength: function() {
            var max = 31;

            if (this.date_of_month.length > max)
            {
                max = this.date_of_month.length
            }
            if (this.month.length > max)
            {
                max = this.month.length
            }
            if (this.year.length > max)
            {
                max = this.year.length
            }
            if (this.annual.length > max)
            {
                max = this.annual.length
            }
            return max;
        }
    },
    methods: {        
        exportPdf() {
            var HTML_Width = jQuery("#report_div").width()*3;
            var HTML_Height = jQuery("#report_div").height();
            var top_left_margin = 5;
            var PDF_Width = HTML_Width + (top_left_margin * 2);
            var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
            var canvas_image_width = HTML_Width;
            var canvas_image_height = HTML_Height;
            
            var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
        
            html2canvas(jQuery("#report_div")[0],{scale:1}).then(function (canvas) {
                var imgData = canvas.toDataURL("image/png", 1.0);
                var pdf = new jsPDF("p", "mm", "a4");
                var width = pdf.internal.pageSize.getWidth();
                var height = pdf.internal.pageSize.getHeight();
                pdf.addImage(imgData, 'PNG', top_left_margin/3, top_left_margin, width, height);
                for (var i = 1; i <= totalPDFPages; i++) { 
                    pdf.addPage(width, canvas_image_height);
                    pdf.addImage(imgData, 'JPG', top_left_margin, -(PDF_Height*i)+(top_left_margin*4),canvas_image_width,canvas_image_height);
                }
                pdf.save("report.pdf");
            });
        },
        async getData() {
            let device_id_select = document.getElementById('device_id');
            this.device_name = device_id_select.options[device_id_select.selectedIndex].text;
            var dateString = document.getElementById("date").value;
            var year = 2020;
            var month = 1;
            var date_of_month = 1;
            if (dateString) {
                var date = new Date(dateString + " 00:00:00");
                year = date.getFullYear();
                month = date.getMonth() + 1;
                date_of_month = date.getDate();
            }        
            var device_id = document.getElementById("device_id").value;
            var uri = encodeURI("/index.php/component/electronic_meter_history?");
            uri = uri + new URLSearchParams({
                task: 'getElectronicMeterStatisticsByPeriod',
                device_id: device_id,
                year:year,
                month:month,
                date_of_month:date_of_month,
                summer_peak_price: this.summer_peak,
                summer_mid_price: this.summer_mid,
                summer_off_peak_price: this.summer_off_peak,
                not_summer_peak_price: this.not_summer_peak,
                not_summer_mid_price: this.not_summer_mid,
                not_summer_off_peak_price: this.not_summer_off_peak                
            });
            const response = await fetch(uri);
            const res_obj = await response.json();
            // console.log(res_obj);
            this.pricing = res_obj.pricing;
            console.log(this.pricing);
            this.annual = res_obj.annual;
            this.year = res_obj.year;
            this.month = res_obj.month;
            this.date_of_month = res_obj.date_of_month;
            this.selected_date = year + '/' + month + '/' + date_of_month;            
            this.show_table = true;
            // this.hour_of_day = res_json.hour_of_day;
        },
        getPrice(usage, peak_flag, is_summer) {
            // console.log(usage,peak_flag,is_summer);
            if (is_summer == "1")
            {
                switch (peak_flag) {
                    case "0":  
                        return this.summer_peak * usage;
                    case "1":
                        return this.summer_mid * usage;
                    case "2":
                        return this.summer_off_peak * usage;
                    default:
                        break;
                }
            }
            else
            {
                switch (peak_flag) {
                    case "0":  
                        return this.not_summer_peak * usage;
                    case "1":
                        return this.not_summer_mid * usage;
                    case "2":
                        return this.not_summer_off_peak * usage;
                    default:
                        break;
                }
            }
            return 999;
        },
        refreshYearly(yearly) { 
            this.yearlyData = yearly;   
            console.log(this.yearlyData);    
        },
        refreshMonthly(monthly) { 
            this.monthlyData = monthly;   
            console.log(this.monthlyData);    
        },
        refreshDaily(daily) { 
            this.dailyData = daily;   
            console.log(this.dailyData);    
        },
        refreshHourly(hourly) { 
            this.hourlyData = hourly;   
            console.log(this.hourlyData);    
        }
    }
});


var export_xlsx = function () {
    jQuery('.chart-div').show();
    var dateString = document.getElementById("date").value;
    var year = 2020;
    var month = 1;
    var date_of_month = 1;
    var default_date = new Date(year, month, date_of_month);
    selectedDateString = default_date.getFullYear() + "/" + (default_date.getMonth() + 1) + "/" + default_date.getDate();
    if (dateString) {
        var date = new Date(dateString + " 00:00:00");
        selectedDateString = date.getFullYear() + "/" + (date.getMonth() + 1) + "/" + date.getDate();
        year = date.getFullYear();
        month = date.getMonth() + 1;
        date_of_month = date.getDate();
    }
    var device_id = document.getElementById("device_id").value;

    console.log(hourlyTable);
    console.log(dailyTable);
    console.log(monthlyTable);
    console.log(annualTable);
    populate_xlsx();
    return false;
};

var populate_xlsx = function () {
    var ws_name = "電力：" + deviceInfo.name;
    var wb = XLSX.utils.book_new();
    /* make worksheet */
    var ws_data = [
        [deviceInfo.name, "時"].concat(hourlyTable.map(function (d) {
            return d.hour_of_day;
        })),
        [selectedDateString, "時電量"].concat(hourlyTable.map(function (d) {
            return d.usage;
        })),
        ["每度費用：", "累積電量"].concat(hourlyTable.map(function (d) {
            return d.accumulate;
        })),
        [1, "計算費用"].concat(hourlyTable.map(function (d, index) {
            return { t: 'n', f: 'B' + (index + 1 + 2) + "*D1" };
        })),
        ["元"],
        ["", "日"].concat(dailyTable.map(function (d) {
            return d.date_of_month;
        })),
        ["", "日電量"].concat(dailyTable.map(function (d) {
            return d.usage;
        })),
        ["", "累積電量"].concat(dailyTable.map(function (d) {
            return d.accumulate;
        })),
        ["", "計算費用"].concat(dailyTable.map(function (d, index) {
            return { t: 'n', f: 'G' + (index + 1 + 2) + "*D1" };
        })),
        [],
        ["", "月"].concat(monthlyTable.map(function (d) {
            return d.month;
        })),
        ["", "月電量"].concat(monthlyTable.map(function (d) {
            return d.usage;
        })),
        ["", "累積電量"].concat(monthlyTable.map(function (d) {
            return d.accumulate;
        })),
        ["", "計算費用"].concat(monthlyTable.map(function (d, index) {
            return { t: 'n', f: 'L' + (index + 1 + 2) + "*D1" };
        })),
        [],
        ["", "年"].concat(annualTable.map(function (d) {
            return d.year;
        })),
        ["", "年電量"].concat(annualTable.map(function (d) {
            return d.usage;
        })),
        ["", "累積電量"].concat(annualTable.map(function (d) {
            return d.accumulate;
        })),
        ["", "計算費用"].concat(annualTable.map(function (d, index) {
            return { t: 'n', f: 'Q' + (index + 1 + 2) + "*D1" };
        })),
        [],
    ];
    console.log("before:", ws_data);
    // ws_data = ws_data[0].map((_, colIndex) => ws_data.map(row => row[colIndex]));
    ws_data = transposeArray(ws_data);
    console.log("after:", ws_data);

    var ws = XLSX.utils.aoa_to_sheet(ws_data);

    /* Add the worksheet to the workbook */
    XLSX.utils.book_append_sheet(wb, ws, ws_name);
    console.log(wb);
    var wopts = { bookType: 'xlsx', bookSST: false, type: 'array' };

    var wbout = XLSX.write(wb, wopts);

    /* the saveAs call downloads a file on the local machine */
    saveAs(new Blob([wbout], { type: "application/octet-stream" }), selectedDateString + "_" + deviceInfo.name + ".xlsx");

};
var transposeArray = function (array) {
    var newArray = [];
    var maxLength = 0;
    for (var i = 0; i < array.length; i++) {
        if (array[i].length > maxLength) {
            maxLength = array[i].length;
        }
    };
    maxLength = 50;
    for (let i = 0; i < maxLength; i++) {
        newArray.push([]);
        for (let j = 0; j < array.length; j++) {
            newArray[i].push([]);
        }
    }
    for (var i = 0; i < maxLength; i++) {
        if (array[i]) {
            for (var j = 0; j < array[i].length; j++) {
                newArray[j][i] = array[i][j];
            };
        } else {
            newArray[j][i] = '';
        }

    };

    return newArray;
};
var submit = function () {
    // var chartsContainerDiv = document.getElementById("chart-div");
    // chartsContainerDiv.style.display = "block";
    jQuery('.chart-div').show();
    var dateString = document.getElementById("date").value;
    var year = 2020;
    var month = 1;
    var date_of_month = 1;
    if (dateString) {
        var date = new Date(dateString + " 00:00:00");
        year = date.getFullYear();
        month = date.getMonth() + 1;
        date_of_month = date.getDate();
    }

    // var range_type = document.getElementById("range_type").value;
    var device_id = document.getElementById("device_id").value;


    empty_data();
    getHourlyChartDataAndDrawChart(device_id, year, month, date_of_month);
    getDailyChartDataAndDrawChart(device_id, year, month);
    getMonthlyChartDataAndDrawChart(device_id, year);
    getAnnualChartDataAndDrawChart(device_id);
    getDeviceInfo(device_id);
    return false;
};
var getDeviceInfo = function (device_id) {
    var uri = encodeURI("/index.php/component/electronic_meter_history?task=getElectronicMeterInfo&device_id=" + device_id);
    jQuery.get(uri, function (resultData) {
        var result = JSON.parse(resultData);
        deviceInfo = { name: result.info };
    }).fail(function () {
        deviceInfo = { name: "" };
    });
};
var empty_data = function () {
    publishAnnualTable([], '', '');
    drawAnnualChart([], '', '');
    publishMonthlyTable([], '', '');
    drawMonthlyChart([], '', '');
    publishDailyTable([], '', '');
    drawDailyChart([], '', '');
    publishHourlyTable([], '', '');
    drawHourlyChart([], '', '');
};
var publishHourlyTable = function (items, labels, label) {
    var hourlyTbody = document.getElementById("hourlyTbody");
    hourlyTbody.innerHTML = '';
    items.forEach((item, index, array) => {
        var tr = document.createElement("tr");
        var tdItemLabel = document.createElement("td");
        tdItemLabel.innerText = labels[index];
        var tdItemUsage = document.createElement("td");
        tdItemUsage.innerText = item.usage;
        var tdItemAccumulate = document.createElement("td");
        tdItemAccumulate.innerText = item.accumulate;
        tr.title = item.update_time;
        tr.appendChild(tdItemLabel);
        tr.appendChild(tdItemUsage);
        tr.appendChild(tdItemAccumulate);
        hourlyTbody.appendChild(tr);
    });
};
var drawHourlyChart = function (items, labels, label) {
    var parent = document.getElementById("hourlyChart").parentNode;
    document.getElementById("hourlyChart").remove();
    var canvas = document.createElement("canvas");
    canvas.id = "hourlyChart";
    parent.appendChild(canvas);
    var ctx = canvas.getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: items,
                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }

    });
};
var publishDailyTable = function (items, labels, label) {
    var dailyTbody = document.getElementById("dailyTbody");
    dailyTbody.innerHTML = '';
    items.forEach((item, index, array) => {
        var tr = document.createElement("tr");
        var tdItemLabel = document.createElement("td");
        tdItemLabel.innerText = labels[index];
        var tdItemUsage = document.createElement("td");
        tdItemUsage.innerText = item.usage;
        var tdItemAccumulate = document.createElement("td");
        tdItemAccumulate.innerText = item.accumulate;
        tr.appendChild(tdItemLabel);
        tr.appendChild(tdItemUsage);
        tr.appendChild(tdItemAccumulate);
        dailyTbody.appendChild(tr);
    });
};
var drawDailyChart = function (items, labels, label) {
    var chartParent = document.getElementById("dailyChart").parentNode;
    document.getElementById("dailyChart").remove();
    var canvas = document.createElement("canvas");
    canvas.id = "dailyChart";
    chartParent.appendChild(canvas);
    var ctx = canvas.getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: items,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
};
var publishMonthlyTable = function (items, labels, label) {
    var monthlyTbody = document.getElementById("monthlyTbody");
    monthlyTbody.innerHTML = '';
    items.forEach((item, index, array) => {
        var tr = document.createElement("tr");
        var tdItemLabel = document.createElement("td");
        tdItemLabel.innerText = labels[index];
        var tdItemUsage = document.createElement("td");
        tdItemUsage.innerText = item.usage;
        var tdItemAccumulate = document.createElement("td");
        tdItemAccumulate.innerText = item.accumulate;
        tr.appendChild(tdItemLabel);
        tr.appendChild(tdItemUsage);
        tr.appendChild(tdItemAccumulate);
        monthlyTbody.appendChild(tr);
    });
};
var drawMonthlyChart = function (items, labels, label) {
    var parent = document.getElementById("monthlyChart").parentNode;
    document.getElementById("monthlyChart").remove();
    var canvas = document.createElement("canvas");
    canvas.id = "monthlyChart";
    parent.appendChild(canvas);
    var ctx = canvas.getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: items,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
};
var publishAnnualTable = function (items, labels, label) {
    var annualTbody = document.getElementById("annualTbody");
    annualTbody.innerHTML = '';
    items.forEach((item, index, array) => {
        var tr = document.createElement("tr");
        var tdItemLabel = document.createElement("td");
        tdItemLabel.innerText = labels[index];
        var tdItemUsage = document.createElement("td");
        tdItemUsage.innerText = item.usage;
        var tdItemAccumulate = document.createElement("td");
        tdItemAccumulate.innerText = item.accumulate;
        tr.appendChild(tdItemLabel);
        tr.appendChild(tdItemUsage);
        tr.appendChild(tdItemAccumulate);
        annualTbody.appendChild(tr);
    });
};
var drawAnnualChart = function (items, labels, label) {
    var parent = document.getElementById("annualChart").parentNode;
    document.getElementById("annualChart").remove();
    var canvas = document.createElement("canvas");
    canvas.id = "annualChart";
    parent.appendChild(canvas);
    var ctx = canvas.getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: items,
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
};
function roundBigInt(num) {
    // Ensure the multiplier is a power of 10 based on the desired decimal places
    const multiplier = Math.pow(10, 3);
    
    // Use Math.round to round the number and then divide by the multiplier to restore the decimal places
    return Math.round(num * multiplier) / multiplier;
}
var getHourlyChartDataAndDrawChart = function (device_id, year, month, date_of_month) {
    var uri = encodeURI("/index.php/component/electronic_meter_history?task=getElectronicMeterStatisticsBySeason&year=" + year + "&month=" + month + "&date_of_month=" + date_of_month + "&range_type=0&device_id=" + device_id);
    jQuery.get(uri, function (resultData) {
        var result = JSON.parse(resultData);
        var resultItems = result.items;
        var lastRecord = result.lastRecord;
    
        hourlyTable = resultItems.map((item, index) => {
            var lastAccumulate = 0;
            if (lastRecord != null)
            {
                lastAccumulate = lastRecord.accumulatepower;
            }
            if (index > 0) {
                return { 
                    is_summer: item.is_summer,
                    hour_of_day: item.hour_of_day, 
                    first_half_peak: item.first_half_hour_peak,
                    last_half_peak: item.last_half_hour_peak,
                    first_half_usage : roundBigInt((item.first_half_hour_accmulatepower - resultItems[index - 1].accumulatepower)) ,
                    last_half_usage : roundBigInt((item.last_half_hour_accmulatepower - item.first_half_hour_accmulatepower)) ,
                    usage: roundBigInt((item.accumulatepower - resultItems[index - 1].accumulatepower)) , 
                    accumulate: item.accumulatepower, 
                    update_time: item.update_time };
            } 
            else {
                return { 
                    is_summer: item.is_summer,
                    hour_of_day: item.hour_of_day,      
                    first_half_peak: item.first_half_hour_peak,
                    last_half_peak: item.last_half_hour_peak,              
                    first_half_usage : roundBigInt((item.first_half_hour_accmulatepower - lastAccumulate)) ,
                    last_half_usage : roundBigInt((item.last_half_hour_accmulatepower - item.first_half_hour_accmulatepower)) ,
                    usage: roundBigInt((item.accumulatepower - lastAccumulate)) , 
                    accumulate: item.accumulatepower, 
                    update_time: item.update_time };
            }
        });
        app.refreshHourly(hourlyTable);
        app.show_table = false;
        console.log(hourlyTable);
        console.log(hourlyChart);
        publishHourlyTable(
            hourlyTable,
            resultItems.map(item => {
                return item.hour_of_day + "時";
            })
        );
        drawHourlyChart(
            resultItems.map((item, index) => {
                if (index > 0) {
                    return Math.round((item.accumulatepower - resultItems[index - 1].accumulatepower) * 1000) / 1000;
                } else {
                    return Math.round((lastRecord ? item.accumulatepower - lastRecord.accumulatepower : 0) * 1000) / 1000;
                }
            }),
            resultItems.map(item => {
                return item.hour_of_day + "時";
            }),
            year + "年" + month + "月" + date_of_month + "日 統計，單位（度）"
        );
        // hourlyChartData = resultItems;
    }).fail(function () {

    });
};
var getExportDataAndExport =  function (device_ids, year,month,date_of_month) {
    var getXlsxUri = encodeURI("/index.php/component/electronic_meter_history?task=getXlsx");
    var dateString = document.getElementById("date_for_export").value;
    var year = 2020;
    var month = 1;
    var date_of_month = 1;
    if (dateString) {
        var date = new Date(dateString + " 00:00:00");
        year = date.getFullYear();
        month = date.getMonth() + 1;
        date_of_month = date.getDate();
    }

    let formData = new FormData();
    // formData.append('device_ids[]', jQuery('#device_id_for_export').val());
    formData.append('year',year);
    formData.append('month',month);
    formData.append('date_of_month',date_of_month);
    var device_ids = jQuery('#device_id_for_export').val();
	device_ids.forEach(function(item,index) {
		formData.append('device_ids[]', item);
	});

    fetch(getXlsxUri,{
		method: 'POST',
		body: formData
	})
    // .then(response => console.log(response));
	.then(response => response.blob())
	.then(blob => {
		var url = window.URL.createObjectURL(blob);
		var a = document.createElement('a');
		a.href = url;
		a.download = "report.xlsx";
		document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
		a.click();    
		a.remove();  //afterwards we remove the element again    
	});
    return false;

    //  jQuery.post(getXlsxUri,formData, function (resultData) {
    //     var resultItems = JSON.parse(resultData);
    //     dailyTable = resultItems.map(item => {
    //         return { date_of_month: item.date_of_month, usage: item.diff, accumulate: item.maxAccumulatepower };
    //     });
    //     console.log(dailyTable);
    //     // app.refreshDaily(dailyTable);
    //     // publishDailyTable(
    //     //     dailyTable,
    //     //     resultItems.map(item => {
    //     //         return item.date_of_month + "日";
    //     //     })
    //     // );
    //     // drawDailyChart(
    //     //     resultItems.map(item => {
    //     //         return item.diff;
    //     //     }),
    //     //     resultItems.map(item => {
    //     //         return item.date_of_month + "日";
    //     //     }),
    //     //     year + "年" + month + "月 統計，單位（度）"
    //     // );
    //     // dailyChartData = resultItems;
    // }).fail(function () {

    // });
};
var getDailyChartDataAndDrawChart = function (device_id, year, month) {
    var uri = encodeURI("/index.php/component/electronic_meter_history?task=getElectronicMeterStatistics&year=" + year + "&month=" + month + "&range_type=1&device_id=" + device_id);
    jQuery.get(uri, function (resultData) {
        var resultItems = JSON.parse(resultData);
        dailyTable = resultItems.map(item => {
            return { date_of_month: item.date_of_month, usage: item.diff, accumulate: item.maxAccumulatepower };
        });
        app.refreshDaily(dailyTable);
        publishDailyTable(
            dailyTable,
            resultItems.map(item => {
                return item.date_of_month + "日";
            })
        );
        drawDailyChart(
            resultItems.map(item => {
                return item.diff;
            }),
            resultItems.map(item => {
                return item.date_of_month + "日";
            }),
            year + "年" + month + "月 統計，單位（度）"
        );
        // dailyChartData = resultItems;
    }).fail(function () {

    });
};
var getMonthlyChartDataAndDrawChart = function (device_id, year) {
    var uri = encodeURI("/index.php/component/electronic_meter_history?task=getElectronicMeterStatistics&year=" + year + "&range_type=2&device_id=" + device_id);
    jQuery.get(uri, function (resultData) {
        var resultItems = JSON.parse(resultData);
        monthlyTable = resultItems.map((item, index) => {
            return { month: item.month, usage: item.diff, accumulate: item.maxAccumulatepower };
        });
        app.refreshMonthly(monthlyTable);
        publishMonthlyTable(
            monthlyTable,
            resultItems.map(item => {
                return item.month + "月";
            })
        );
        drawMonthlyChart(
            resultItems.map(item => {
                return item.diff;
            }),
            resultItems.map(item => {
                return item.month + "月";
            }),
            year + "年 統計，單位（度）");
        // monthlyChartData = resultItems;
    }).fail(function () {

    });
};
var getAnnualChartDataAndDrawChart = function (device_id) {

    var uri = encodeURI("/index.php/component/electronic_meter_history?task=getElectronicMeterStatistics&range_type=3&device_id=" + device_id);
    jQuery.get(uri, function (resultData) {
        var resultItems = JSON.parse(resultData);
        annualTable = resultItems.map((item, index) => {
            return { year: item.year, usage: item.diff, accumulate: item.maxAccumulatepower };
        });
        app.refreshYearly(annualTable);
        publishAnnualTable(
            annualTable,
            resultItems.map(item => {
                return item.year + "年";
            })
        );
        drawAnnualChart(
            resultItems.map(item => {
                return item.diff;
            }),
            resultItems.map(item => {
                return item.year + "年";
            }),
            "歷年統計，單位（度）");
        // annualChartData = resultItems;
    }).fail(function () {

    });
};
jQuery(function () {
    const datetime = new Date();
    const year = datetime.getFullYear();
    const month = (datetime.getMonth() + 1).toString().padStart(2, '0');
    const day = datetime.getDate().toString().padStart(2, '0');
    jQuery('#date').val(`${year}-${month}-${day}`).change();

    const url = new URLSearchParams(window.location.search);
    const device_id = url.get('device_id');
    const meter_type = url.get('meter_type');
    let optg_electronic_meter = jQuery('#optg_electronic_meter');
    let optg_electronic_meter_for_export = jQuery('#optg_electronic_meter_for_export');
    let optg_solar_meter = jQuery('#optg_solar_meter');
    let optg_solar_meter_for_export = jQuery('#optg_solar_meter_for_export');
    var refresh_device_id_select = function (device_id) {
        if (device_id != null) {
            jQuery('#device_id').chosen('destroy').val(device_id).change().chosen();
        }
        else {
            jQuery('#device_id').chosen('destroy').chosen();
        }
    };
    var refresh_device_id_select_for_export = function (device_id) {
        if (device_id != null) {
            jQuery('#device_id_for_export').chosen('destroy').val(device_id).change().chosen();
        }
        else {
            jQuery('#device_id_for_export').chosen('destroy').chosen();
        }
    };
    var show_electronic_meters = function () {
        optg_solar_meter.remove();
        jQuery('#device_id').append(optg_electronic_meter);
        // refresh_device_id_select();        
    };
    var show_electronic_meters_for_export = function () {
        optg_solar_meter_for_export.remove();
        jQuery('#device_id_for_export').append(optg_electronic_meter_for_export);
        // refresh_device_id_select();        
    };
    var show_solar_meters = function () {
        optg_electronic_meter.remove();
        jQuery('#device_id').append(optg_solar_meter);
        // refresh_device_id_select();        
    };
    var show_solar_meters_for_export = function () {
        optg_electronic_meter_for_export.remove();
        jQuery('#device_id_for_export').append(optg_solar_meter_for_export);
        // refresh_device_id_select();        
    };
    jQuery('#meter_type_for_export').change(function () {
        let meter_type = jQuery('#meter_type_for_export');
        if (meter_type.val()) {
            // empty_data();
            jQuery('#device_id_for_export').parent().show();
            if (meter_type.val().toLowerCase() == 'electronicmeter') {
                jQuery('.electronic-meter_for_export').show();
                jQuery('.solar-meter_for_export').hide();
                // jQuery('#chart-div').hide();
                // jQuery('.meter-type').each(function () {
                //     jQuery(this).html(function (index, html) {
                //         return html.replace('發電', '用電');
                //     });
                // });
                jQuery('#optg_electronic_meter_for_export').show();
                jQuery('#solar_electronic_meter_for_export').hide();
                show_electronic_meters_for_export();
                refresh_device_id_select_for_export();
                // refresh_device_id_select();

            }
            else if (meter_type.val().toLowerCase() == 'solarmeter') {
                jQuery('.electronic-meter_for_export').hide();
                jQuery('.solar-meter_for_export').show();
                // jQuery('#chart-div').hide();
                // jQuery('.meter-type').each(function () {
                //     jQuery(this).html(function (index, html) {
                //         return html.replace('用電', '發電');
                //     });
                // });
                jQuery('#optg_electronic_meter_for_export').hide();
                jQuery('#solar_electronic_meter_for_export').show();
                // refresh_device_id_select();
                show_solar_meters_for_export();
                refresh_device_id_select_for_export();
            }
        }
    });
    jQuery('#meter_type').change(function () {
        let meter_type = jQuery('#meter_type');

        if (jQuery('#meter_type').val()) {
            empty_data();
            jQuery('#device_id').parent().show();
            if (jQuery('#meter_type').val().toLowerCase() == 'electronicmeter') {
                jQuery('.electronic-meter').show();
                jQuery('.solar-meter').hide();
                // jQuery('#chart-div').hide();
                jQuery('.meter-type').each(function () {
                    jQuery(this).html(function (index, html) {
                        return html.replace('發電', '用電');
                    });
                });
                jQuery('#optg_electronic_meter').show();
                jQuery('#solar_electronic_meter').hide();
                show_electronic_meters();
                refresh_device_id_select();
                // refresh_device_id_select();

            }
            else if (jQuery('#meter_type').val().toLowerCase() == 'solarmeter') {
                jQuery('.electronic-meter').hide();
                jQuery('.solar-meter').show();
                // jQuery('#chart-div').hide();
                jQuery('.meter-type').each(function () {
                    jQuery(this).html(function (index, html) {
                        return html.replace('用電', '發電');
                    });
                });
                jQuery('#optg_electronic_meter').hide();
                jQuery('#solar_electronic_meter').show();
                // refresh_device_id_select();
                show_solar_meters();
                refresh_device_id_select();
            }
        }
    });
    if (meter_type != null) {
        switch (meter_type.toLowerCase()) {
            case 'electronicmeter':
                jQuery('#meter_type').chosen('destroy').val('ElectronicMeter').change().chosen();
                // jQuery('.electronic-meter').show();
                // jQuery('.solar-meter').hide();
                break;
            case 'solarmeter':
                jQuery('#meter_type').chosen('destroy').val('SolarMeter').change().chosen();
                // jQuery('.electronic-meter').hide();
                // jQuery('.solar-meter').show();
                break;
            default:
                break;
        }
    }
    else {

    }
    if (device_id != null) {
        console.log(device_id);
        refresh_device_id_select(device_id);
        // jQuery('#device_id').chosen('destroy').val(device_id).change().chosen();
        submit();
    }
});