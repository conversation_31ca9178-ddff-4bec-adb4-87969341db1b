<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Electronic_meter_history
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 Hu Yuwei
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Chart\Chart;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;
use PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues;
use PhpOffice\PhpSpreadsheet\Chart\Layout;
use PhpOffice\PhpSpreadsheet\Chart\Legend as ChartLegend;
use PhpOffice\PhpSpreadsheet\Chart\PlotArea;
use PhpOffice\PhpSpreadsheet\Chart\Title;
use PhpOffice\PhpSpreadsheet\IOFactory;
jimport('joomla.application.component.controller');

use \Joomla\CMS\Factory;

/**
 * Class Electronic_meter_historyController
 *
 * @since  1.6
 */
class Electronic_meter_historyController extends \Joomla\CMS\MVC\Controller\BaseController
{
	/**
	 * Method to display a view.
	 *
	 * @param   boolean $cachable  If true, the view output will be cached
	 * @param   mixed   $urlparams An array of safe url parameters and their variable types, for valid values see {@link JFilterInput::clean()}.
	 *
	 * @return  JController   This object to support chaining.
	 *
	 * @since    1.5
	 * @throws Exception
	 */
	public function display($cachable = false, $urlparams = false)
	{
		$app  = Factory::getApplication();
		$view = $app->input->getCmd('view', 'electronicmeterhistory');
		$app->input->set('view', $view);

		parent::display($cachable, $urlparams);

		return $this;
	}
	public function getElectronicMeterInfo()
	{
		$app  = JFactory::getApplication();
		$device_id = $app->input->getInt('device_id', -1);
		$result = Electronic_meter_historyHelpersElectronic_meter_history::get_electronic_meter_info($device_id);
		echo (json_encode($result));
		JFactory::getApplication()->close();
	}
	public function getElectronicMeterStatisticsByPeriod()
	{
		$summer = new StdClass();
		$summer->peak = 2.32;
		$summer->mid = 1.42;
		$summer->off_peak = 0.91;
		$not_summer = new StdClass();
		$not_summer->peak = 2.24;
		$not_summer->mid = 1.35;
		$not_summer->off_peak = 0.84;

		$pricing = new StdClass();
		$pricing->summer = $summer;
		$pricing->not_summer = $not_summer;
		
		$app  = JFactory::getApplication();
		$pricing->summer->peak = $app->input->getVar('summer_peak_price',$pricing->summer->peak);
		$pricing->summer->mid = $app->input->getVar('summer_mid_price',$pricing->summer->mid);
		$pricing->summer->off_peak = $app->input->getVar('summer_off_peak_price',$pricing->summer->off_peak);
		$pricing->not_summer->peak = $app->input->getVar('not_summer_peak_price',$pricing->not_summer->peak);
		$pricing->not_summer->mid = $app->input->getVar('not_summer_mid_price',$pricing->not_summer->mid);
		$pricing->not_summer->off_peak = $app->input->getVar('not_summer_off_peak_price',$pricing->not_summer->off_peak);

		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		$hour_of_day = $app->input->getInt('hour_of_day', -1);
		$items = new StdClass();
		$items->pricing = $pricing;
		$items->annual =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,-1,-1,-1,-1);
		$items->year =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,-1,-1,-1);
		$items->month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,-1,-1);
		$items->date_of_month =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		// $items->hour_of_day =  Electronic_meter_historyHelpersElectronic_meter_history::get_pre_calculated_price_records($pricing, $device_id,$year,$month,$date_of_month,-1);
		echo(json_encode($items));
		$app->close();

	}
	public function getElectronicMeterStatisticsBySeason()
	{
		$app  = JFactory::getApplication();
		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);
		
		$summer = new StdClass();
		$summer->peak = 2.32;
		$summer->mid = 1.42;
		$summer->off_peak = 0.91;
		$not_summer = new StdClass();
		$not_summer->peak = 2.24;
		$not_summer->mid = 1.35;
		$not_summer->off_peak = 0.84;

		$p = array($summer, $not_summer);
		
		if ($range_type == 0) //hourly
		{
			$result = new stdClass();
			$result->items = Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics_by_period($device_id, $year, $month, $date_of_month);		
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);			
			$result->lastRecord = count($lastRecord) >0 ? $lastRecord[0] : null;		
			echo (json_encode($result));
		} else if ($range_type == 1) //daily
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 2) //monthly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_monthly_electronic_meter_statistics($device_id, $year);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 3) // yearly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_annual_electronic_meter_statistics($device_id);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minAccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minAccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minAccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		}
		JFactory::getApplication()->close();
	}
	public function getXlsx()
	{


		$app = JFactory::getApplication();
		// print_r($app->input->post);
		// $app->close();
		// return;
		// print_r($app);
		$device_ids = $app->input->get('device_ids', array(),'array');
		// $start_date = $app->input->post->getVar('start_date', '');
		$year = $app->input->post->getInt('year', 2023);
		$month = $app->input->post->getInt('month', 1);
		$date_of_month = $app->input->post->getInt('date_of_month',1);
		
		$daily = array();
		$monthly = array();
		$yearly = array();
		$info = array();
		foreach ($device_ids as $key => $device_id) {
			$info[$device_id] = Electronic_meter_historyHelpersElectronic_meter_history::get_electronic_meter_info($device_id)->info;
			//get daily		
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			$daily[$device_id] = $records;
			
			//get monthly
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_monthly_electronic_meter_statistics($device_id, $year);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			$monthly[$device_id] = $records;

			//get yearly
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_annual_electronic_meter_statistics($device_id);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minAccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minAccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minAccumulatepower, 3,".","");
			}

			$yearly[$device_id] = $records;
		}
		$result_obj = new StdClass();
		$result_obj->daily = $daily;
		$result_obj->monthly = $monthly;
		$result_obj->yearly = $yearly;
		$result_obj->device_ids = $device_ids;
		// echo json_encode($result_obj);
		// JFactory::getApplication()->close();


		require_once JPATH_LIBRARIES . '/phpspreadsheet/vendor/autoload.php';
		require_once JPATH_LIBRARIES . '/phpspreadsheet/phpspreadsheet.php';
		$spreadsheet = new Spreadsheet();

		$spreadsheet->createSheet();
		$spreadsheet->createSheet();
		$spreadsheet->createSheet();
		$sheet0 = $spreadsheet->getSheet(0);
		$sheet1 = $spreadsheet->getSheet(1);
		$sheet2 = $spreadsheet->getSheet(2);
		$sheet3 = $spreadsheet->getSheet(3);
		// $sheet3Array = 
		// 	[];
		// $sheet3->fromArray(
		// 	$sheet3Array
		// );
		
		$sheet0Title = $year.'-'.$month.'-'.$date_of_month.'日資料';
		$sheet1Title = $year.'-'.$month.'月資料';
		$sheet2Title = $year.'年資料';
		$sheet0->setTitle($sheet0Title);
		$sheet1->setTitle($sheet1Title);
		$sheet2->setTitle($sheet2Title);
		$worksheet = $spreadsheet->getActiveSheet();

		
		$sheet0Array = [
			['裝置','佔比%','用電量(度)']
			];
		$sheet0Sum = 0;
		foreach ($device_ids as $key => $device_id) {			
			$sum = 0;
			foreach ($daily[$device_id] as $key1 => $item) {
				if ($item->date_of_month == $date_of_month)
				{
					$sum = $item->diff;
					break;
				}
			}	
			$sheet0Sum = $sheet0Sum + $sum;
		}
		foreach ($device_ids as $key => $device_id) {
			$sum = 0;
			foreach ($daily[$device_id] as $key1 => $item) {
				if ($item->date_of_month == $date_of_month)
				{
					$sum = $item->diff;
					break;
				}
			}
			array_push($sheet0Array,[$info[$device_id],100*($sum/$sheet0Sum),$sum]);
		}
		array_push($sheet0Array,['小計','',$sheet0Sum]);
		$sheet0->fromArray(
			$sheet0Array
		);
		

		$sheet1Array = [
			['裝置','佔比%','用電量(度)']
			];
		$sheet1Sum = 0;
		foreach ($device_ids as $key => $device_id) {
			$sum = 0;
			foreach ($monthly[$device_id] as $key1 => $item) {
				if ($item->month == $month)
				{
					$sum = $item->diff;
					break;
				}
			}
			// $sum = array_reduce($daily[$device_id],function($carry,$item){
			// 	return $carry + ($item->diff);
			// },0);	
			$sheet1Sum = $sheet1Sum + $sum;
		}
		foreach ($device_ids as $key => $device_id) {
			$sum = 0;
			foreach ($monthly[$device_id] as $key1 => $item) {
				if ($item->month == $month)
				{
					$sum = $item->diff;
					break;
				}
			}	
			// $monthly[$device_id]->
			// $sum = array_reduce($daily[$device_id],function($carry,$item){
			// 	return $carry + ($item->diff);
			// },0);
			array_push($sheet1Array,[$info[$device_id],100*($sum/$sheet1Sum),$sum]);
		}
		array_push($sheet1Array,['小計','',$sheet1Sum]);
		$sheet1->fromArray(
			$sheet1Array
		);

		$sheet2Array = [
			['裝置','佔比%','用電量(度)']
			];
		$sheet2Sum = 0;
		foreach ($device_ids as $key => $device_id) {		
			$sum = 0;
			foreach ($yearly[$device_id] as $key1 => $item) {
				if ($item->year == $year)
				{
					$sum = $item->diff;
					break;
				}
			}	
			// $sum = array_reduce($monthly[$device_id],function($carry,$item){
			// 	return $carry + ($item->diff);
			// },0);	
			$sheet2Sum = $sheet2Sum + $sum;
		}
		foreach ($device_ids as $key => $device_id) {
			$sum = 0;
			foreach ($yearly[$device_id] as $key1 => $item) {
				if ($item->year == $year)
				{
					$sum = $item->diff;
					break;
				}
			}	
			// $monthly[$device_id]->
			// $sum = array_reduce($monthly[$device_id],function($carry,$item){
			// 	return $carry + ($item->diff);
			// },0);
			array_push($sheet2Array,[$info[$device_id],100*($sum/$sheet2Sum),$sum]);
		}
		array_push($sheet2Array,['小計','',$sheet2Sum]);
		$sheet2->fromArray(
			$sheet2Array
		);
		// array_push($sheet1Array,$sheet_rows);
		// echo json_encode($sheet1Array);
		// JFactory::getApplication()->close();


		// $items = [
		// 	['項目', '次數','日期(起)','日期(訖)'],
		// ];
		// foreach ($histories as $history)
		// {						
		// 	array_push($items, [
		// 		$history->name,
		// 		strval($history->count)
		// 	]);
		// }

		// if (count($items) > 1)
		// {
		// 	array_push($items[1], $start_date,$end_date);
		// }		
		// else
		// {
		// 	array_push($items, ['','',$start_date,$end_date]);
		// }
		
		// $worksheet->fromArray(
		// 	$items
		// );
		$data_len = count($device_ids)+1;
		// // Set the Labels for each data series we want to plot
		//     Datatype
		//     Cell reference for data
		//     Format Code
		//     Number of datapoints in series
		//     Data values
		//     Data Marker
		$dataSeriesLabels0 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet0Title.'\'!$D$1', null, 1), // 2011
		];
		$dataSeriesLabels1 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet1Title.'\'!$D$1', null, 1), // 2011
		];
		$dataSeriesLabels2 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet2Title.'\'!$D$1', null, 1), // 2011
		];
		// Set the X-Axis Labels
		//     Datatype
		//     Cell reference for data
		//     Format Code
		//     Number of datapoints in series
		//     Data values
		//     Data Marker
		$xAxisTickValues0 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet0Title.'\'!$A$2:$A$'.$data_len, null, 4), // Q1 to Q4
		];
		$xAxisTickValues1 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet1Title.'\'!$A$2:$A$'.$data_len, null, 4), // Q1 to Q4
		];
		$xAxisTickValues2 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, '\''.$sheet2Title.'\'!$A$2:$A$'.$data_len, null, 4), // Q1 to Q4
		];
		// Set the Data values for each data series we want to plot
		//     Datatype
		//     Cell reference for data
		//     Format Code
		//     Number of datapoints in series
		//     Data values
		//     Data Marker
		$dataSeriesValues0 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, '\''.$sheet0Title.'\'!$C$2:$C$'.$data_len, null, 4),
		];
		$dataSeriesValues1 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, '\''.$sheet1Title.'\'!$C$2:$C$'.$data_len, null, 4),
		];
		$dataSeriesValues2 = [
			new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, '\''.$sheet2Title.'\'!$C$2:$C$'.$data_len, null, 4),
		];
		// Build the dataseries
		$series0 = new DataSeries(
			DataSeries::TYPE_PIECHART, // plotType
			null, // plotGrouping (Pie charts don't have any grouping)
			range(0, count($dataSeriesValues0) - 1), // plotOrder
			$dataSeriesLabels0, // plotLabel
			$xAxisTickValues0, // plotCategory
			$dataSeriesValues0          // plotValues
		);
		$series1 = new DataSeries(
			DataSeries::TYPE_PIECHART, // plotType
			null, // plotGrouping (Pie charts don't have any grouping)
			range(0, count($dataSeriesValues1) - 1), // plotOrder
			$dataSeriesLabels1, // plotLabel
			$xAxisTickValues1, // plotCategory
			$dataSeriesValues1          // plotValues
		);
		$series2 = new DataSeries(
			DataSeries::TYPE_PIECHART, // plotType
			null, // plotGrouping (Pie charts don't have any grouping)
			range(0, count($dataSeriesValues2) - 1), // plotOrder
			$dataSeriesLabels2, // plotLabel
			$xAxisTickValues2, // plotCategory
			$dataSeriesValues2          // plotValues
		);
		$layout0 = new Layout();
		$layout0->setShowVal(true);
		$layout0->setShowPercent(true);
		$layout0->setShowCatName(true);
		// Set the series in the plot area
		$plotArea0 = new PlotArea($layout0, [$series0]);
		// Set the chart legend
		$legend0 = new ChartLegend(ChartLegend::POSITION_RIGHT, null, false);

		$title0 = new Title('用電分析');

		// Create the chart
		$chart0 = new Chart(
			'chart0', // name
			$title0, // title
			$legend0, // legend
			$plotArea0, // plotArea
			true, // plotVisibleOnly
			DataSeries::EMPTY_AS_GAP, // displayBlanksAs
			null, // xAxisLabel
			null   // yAxisLabel - Pie charts don't have a Y-Axis
		);
		// Set up a layout object for the Pie chart
		$layout1 = new Layout();
		$layout1->setShowVal(true);
		$layout1->setShowPercent(true);
		$layout1->setShowCatName(true);
		// Set the series in the plot area
		$plotArea1 = new PlotArea($layout1, [$series1]);
		// Set the chart legend
		$legend1 = new ChartLegend(ChartLegend::POSITION_RIGHT, null, false);

		$title1 = new Title('用電分析');

		// Create the chart
		$chart1 = new Chart(
			'chart1', // name
			$title1, // title
			$legend1, // legend
			$plotArea1, // plotArea
			true, // plotVisibleOnly
			DataSeries::EMPTY_AS_GAP, // displayBlanksAs
			null, // xAxisLabel
			null   // yAxisLabel - Pie charts don't have a Y-Axis
		);
		$layout2 = new Layout();
		$layout2->setShowVal(true);
		$layout2->setShowPercent(true);
		$layout2->setShowCatName(true);
		// Set the series in the plot area
		$plotArea2 = new PlotArea($layout2, [$series2]);
		// Set the chart legend
		$legend2 = new ChartLegend(ChartLegend::POSITION_RIGHT, null, false);

		$title2 = new Title('用電分析');

		// Create the chart
		$chart2 = new Chart(
			'chart2', // name
			$title2, // title
			$legend2, // legend
			$plotArea2, // plotArea
			true, // plotVisibleOnly
			DataSeries::EMPTY_AS_GAP, // displayBlanksAs
			null, // xAxisLabel
			null   // yAxisLabel - Pie charts don't have a Y-Axis
		);
		// Set the position where the chart should appear in the worksheet
		$chart0->setTopLeftPosition('E2');
		$chart0->setBottomRightPosition('X38');
		$chart1->setTopLeftPosition('E2');
		$chart1->setBottomRightPosition('X38');
		$chart2->setTopLeftPosition('E2');
		$chart2->setBottomRightPosition('X38');
		// Add the chart to the worksheet
		$sheet0->addChart($chart0);
		$sheet1->addChart($chart1);
		$sheet2->addChart($chart2);

		
		$sheet0->getColumnDimension('A')->setWidth('40');
		$sheet0->getColumnDimension('C')->setWidth('13');
		$sheet0->getColumnDimension('D')->setWidth('13');
		$sheet1->getColumnDimension('A')->setWidth('40');
		$sheet1->getColumnDimension('C')->setWidth('13');
		$sheet1->getColumnDimension('D')->setWidth('13');
		$sheet2->getColumnDimension('A')->setWidth('40');
		$sheet2->getColumnDimension('C')->setWidth('13');
		$sheet2->getColumnDimension('D')->setWidth('13');

		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
		$writer->setIncludeCharts(true);
		$callStartTime = microtime(true);
		$filename = 'x';
		$app
		-> setHeader('Content-Type','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',true)
		-> setHeader('Content-disposition','attachment;filename="'.$filename.'.xlsx";creation-date="'
		.JFactory::getDate()->toRFC822().'"',true);

		ob_end_clean();
		$app->sendHeaders();
		
		$writer->save('php://output');
		$app->close();
	}
	public function getElectronicMeterStatistics()
	{
		$app  = JFactory::getApplication();
		$range_type = $app->input->getInt('range_type', -1);
		$device_id = $app->input->getInt('device_id', -1);
		$year = $app->input->getInt('year', -1);
		$month = $app->input->getInt('month', -1);
		$date_of_month = $app->input->getInt('date_of_month', -1);

		if ($range_type == 0) //hourly
		{
			$result = new stdClass();
			$result->items = Electronic_meter_historyHelpersElectronic_meter_history::get_hourly_electronic_meter_statistics($device_id, $year, $month, $date_of_month);		
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);			
			$result->lastRecord = count($lastRecord) >0 ? $lastRecord[0] : null;		
			echo (json_encode($result));
		} else if ($range_type == 1) //daily
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_daily_electronic_meter_statistics($device_id, $year, $month);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month, $date_of_month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 2) //monthly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_monthly_electronic_meter_statistics($device_id, $year);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year, $month);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minaccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minaccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minaccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		} else if ($range_type == 3) // yearly
		{
			$records = Electronic_meter_historyHelpersElectronic_meter_history::get_annual_electronic_meter_statistics($device_id);
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_electronic_meter_record($device_id, $year);
			$lastValue = count($lastRecord) > 0 ? $lastRecord[0]->accumulatepower : 0;
			$records[0]->minAccumulatepower = $lastValue;
			foreach ($records as $key => $record) {				
				if ($key > 0)
				{
					// $new_lastValue = $records[$key-1]->maxAccumulatepower;
					$records[$key]->minAccumulatepower = $records[$key-1]->maxAccumulatepower;;
					
					// $lastValue = $new_lastValue;					
				}
				$records[$key]->diff = number_format($records[$key]->maxAccumulatepower - $records[$key]->minAccumulatepower, 3,".","");
			}
			echo (json_encode($records));
		}
		JFactory::getApplication()->close();
	}
}
