<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Electronic_meter_history
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 Hu Yu<PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('Electronic_meter_historyHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_electronic_meter_history' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'electronic_meter_history.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

/**
 * Class Electronic_meter_historyFrontendHelper
 *
 * @since  1.6
 */
class Electronic_meter_historyHelpersElectronic_meter_history
{
	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */
	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_electronic_meter_history/models/' . strtolower($name) . '.php')) {
			require_once JPATH_SITE . '/components/com_electronic_meter_history/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'Electronic_meter_historyModel');
		}

		return $model;
	}
	public static function get_all_solar_meter_devices()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('*')
			->from('#__device_table as device')
			// ->order($db->escape('id DESC'))
			->where('device.state = 1')
			->where('device.dio_type IN (19,33,37)');
			// 4=>'温濕度計',
			// 5=>'電表',
			// 6=>'水表',
			// 7=>'一氧化碳',
			// 8=>'智慧路燈',
			// 9=>'巧力電表',
			// 10=>'大同電表',
			// 11=>'永嘉溫濕度計',
			// 12=>'Bender PEM333 電表',
			// 13=>'Bender PEM575 電表',
			// 14=>'士林電機電表',
			// 15=>'久德風向感測器',
			// 16=>'久德雨量計',
			// 17=>'久德土壤計',
			// 18=>'JNC智慧空氣偵測儀',
			// 19=>'新望太陽能
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_all_electronic_meter_devices()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('*')
			->from('#__device_table as device')
			// ->order($db->escape('id DESC'))
			->where('device.state = 1')
			->where('device.dio_type IN (5,9,10,12,13,14,21,23,24,25,29,31,36,43,44,47,50,51,52)');
			//cic
			// 4=>'温濕度計',
			// 5=>'電表',
			// 6=>'水表',
			// 7=>'一氧化碳',
			// 8=>'智慧路燈',
			// 9=>'巧力電表',
			// 10=>'大同電表',
			// 11=>'永嘉溫濕度計',
			// 12=>'Bender PEM333 電表',
			// 13=>'Bender PEM575 電表',
			// 14=>'士林電機電表shihlin',
			// 15=>'久德風向感測器',
			// 16=>'久德雨量計',
			// 17=>'久德土壤計',
			// 18=>'JNC智慧空氣偵測儀',
			// 19=>'新望太陽能
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_pre_calculated_price_records($pricing, $device_id,$year = -1,$month=-1,$date_of_month=-1,$hour_of_day=-1)
	{


		$group_by_string = "";
		$where_string = "device_id = '{$device_id}' ";
		if ($hour_of_day !=-1)
		{
			$group_by_string = "`year`,`month`,`date_of_month`,hour_of_day";
			// $group_by_string1 = "`year`,`month`,`date_of_month`,hour_of_day,is_summer";
			$where_string = "device_id = '{$device_id}'  AND `year` = {$year} AND `month` = {$month} AND date_of_month = {$date_of_month} AND hour_of_day = {$hour_of_day}";
		}
		else if ($date_of_month !=-1)
		{
			$group_by_string = "`year`,`month`,`date_of_month`,hour_of_day";
			// $group_by_string1 = "`year`,`month`,`date_of_month`,is_summer";
			$where_string = "device_id = '{$device_id}'  AND `year` = {$year} AND `month` = {$month} AND date_of_month = {$date_of_month}";
		}
		else if ($month != -1)
		{
			$group_by_string = "`year`,`month`,date_of_month";
			// $group_by_string1 = "`year`,`month`,is_summer";
			$where_string = "device_id = '{$device_id}'  AND `year` = {$year} AND `month` = {$month}";
		}
		else if ($year != -1)
		{
			$group_by_string = "`year`,`month`";
			// $group_by_string1 = "`year`,is_summer";
			$where_string = "device_id = '{$device_id}'  AND `year` = {$year}";
		}
		else
		{
			$group_by_string = "`year`";
			// $group_by_string1 = "`year`,is_summer";
			$where_string = "device_id = '{$device_id}'";
		}

		// $query_level_0 = "SET @summer_peak_price:= {$pricing->summer->peak};
		// SELECT
		// 	`year`,`month`,`date_of_month`,hour_of_day, `month` >=6 && `month` <=9 AS is_summer,
		// 	@wd := WEEKDAY(CONCAT(history.`year`,'-',history.`month`,'-',history.date_of_month))+1 as wd,
		// 	@first_peak :=(@wd > 0 && @wd <6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_peak_flag,
		// 	@first_mid :=(@wd = 6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_mid_flag,
		// 	@first_off_peak := @first_peak = 0 && @first_mid = 0 AS first_half_off_peak_flag,
		// 	@last_peak :=(@wd > 0 && @wd <6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_peak_flag,
		// 	@last_mid :=(@wd = 6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_mid_flag,
		// 	@last_off_peak := @last_peak = 0 && @last_mid = 0 AS last_half_off_peak_flag,
		// 	@first_half_hour_peak:= (@first_off_peak *2 + @first_mid) AS first_half_hour_peak,
		// 	@last_half_hour_peak:= (@last_off_peak *2 + @last_mid) AS last_half_hour_peak,
		// 	CASE WHEN @first_half_hour_peak = 2 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END AS `first_off_peak_usage`,
		// 	CASE WHEN @first_half_hour_peak = 1 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END AS `first_mid_peak_usage`,
		// 	CASE WHEN @first_half_hour_peak = 0 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END AS `first_peak_usage`,
		// 	CASE WHEN @last_half_hour_peak = 2 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END AS `last_off_peak_usage`,
		// 	CASE WHEN @last_half_hour_peak = 1 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END AS `last_mid_peak_usage`,
		// 	CASE WHEN @last_half_hour_peak = 0 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END AS `last_peak_usage`
		// 	FROM `#__electronic_meter_history` as history
		// 	WHERE {$where_string}
		// 	GROUP BY device_id, `year`,`month`,`date_of_month`,hour_of_day, is_summer,first_half_hour_peak,last_half_hour_peak
		// 	ORDER BY device_id, `year`,`month`,`date_of_month`, hour_of_day ASC";

		// $query_level_1 = "(SELECT last_half_hour_accmulatepower,first_half_hour_accmulatepower,last_accumulatepower,device_id,
		// 	`year`,`month`,`date_of_month`,hour_of_day, `month` >=6 && `month` <=9 AS is_summer,
		// 	@wd := WEEKDAY(CONCAT(history.`year`,'-',history.`month`,'-',history.date_of_month))+1 as wd,
		// 	@first_peak :=(@wd > 0 && @wd <6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_peak_flag,
		// 	@first_mid :=(@wd = 6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_mid_flag,
		// 	@first_off_peak := @first_peak = 0 && @first_mid = 0 AS first_half_off_peak_flag,
		// 	@last_peak :=(@wd > 0 && @wd <6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_peak_flag,
		// 	@last_mid :=(@wd = 6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_mid_flag,
		// 	@last_off_peak := @last_peak = 0 && @last_mid = 0 AS last_half_off_peak_flag,
		// 	@first_half_hour_peak:= (@first_off_peak *2 + @first_mid) AS first_half_hour_peak,
		// 	@last_half_hour_peak:= (@last_off_peak *2 + @last_mid) AS last_half_hour_peak,
		// 	SUM(CASE WHEN @first_half_hour_peak = 2 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_off_peak_usage`,
		// 	SUM(CASE WHEN @first_half_hour_peak = 1 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_mid_peak_usage`,
		// 	SUM(CASE WHEN @first_half_hour_peak = 0 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_peak_usage`,
		// 	SUM(CASE WHEN @last_half_hour_peak = 2 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_off_peak_usage`,
		// 	SUM(CASE WHEN @last_half_hour_peak = 1 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_mid_peak_usage`,
		// 	SUM(CASE WHEN @last_half_hour_peak = 0 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_peak_usage`
		// 	FROM `#__electronic_meter_history` as history
		// 	WHERE {$where_string}
		// 	GROUP BY device_id, `year`,`month`,`date_of_month`,hour_of_day, is_summer
		// 	ORDER BY device_id, `year`,`month`,`date_of_month`, hour_of_day ASC) AS history1 ";


		$query_level_0 = "(
			SELECT last_half_hour_accmulatepower,first_half_hour_accmulatepower,last_accumulatepower,device_id,
			`year`,`month`,`date_of_month`,hour_of_day, `month` >=6 && `month` <=9 AS is_summer,
			@wd := WEEKDAY(CONCAT(history.`year`,'-',history.`month`,'-',history.date_of_month))+1 as wd,
			@first_peak :=(@wd > 0 && @wd <6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_peak_flag,
			@first_mid :=(@wd = 6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_mid_flag,
			@first_off_peak := @first_peak = 0 && @first_mid = 0 AS first_half_off_peak_flag,
			@first_half_hour_peak:= (@first_off_peak *2 + @first_mid) AS first_half_hour_peak,

			@last_peak :=(@wd > 0 && @wd <6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_peak_flag,
			@last_mid :=(@wd = 6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_mid_flag,
			@last_off_peak := @last_peak = 0 && @last_mid = 0 AS last_half_off_peak_flag,
			@last_half_hour_peak:= (@last_off_peak *2 + @last_mid) AS last_half_hour_peak

			FROM `#__electronic_meter_history` as history
			WHERE {$where_string}
		)as history";
		$query_level_1 = "(SELECT *,
			SUM(CASE WHEN first_half_hour_peak = 2 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_off_peak_usage`,
			SUM(CASE WHEN first_half_hour_peak = 1 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_mid_peak_usage`,
			SUM(CASE WHEN first_half_hour_peak = 0 THEN history.first_half_hour_accmulatepower - history.last_accumulatepower ELSE 0 END) AS `first_peak_usage`,
			SUM(CASE WHEN last_half_hour_peak = 2 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_off_peak_usage`,
			SUM(CASE WHEN last_half_hour_peak = 1 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_mid_peak_usage`,
			SUM(CASE WHEN last_half_hour_peak = 0 AND history.last_half_hour_accmulatepower!= 0 THEN history.last_half_hour_accmulatepower - history.first_half_hour_accmulatepower ELSE 0 END) AS `last_peak_usage`
			FROM {$query_level_0}
			GROUP BY device_id, `year`,`month`,`date_of_month`,hour_of_day, is_summer
			ORDER BY device_id, `year`,`month`,`date_of_month`, hour_of_day ASC) AS history1 ";

		$query_level_2 = "(SELECT
			`year`,`month`,`date_of_month`,hour_of_day,is_summer,
			history1.first_peak_usage+history1.last_peak_usage AS peak_usage,
			history1.first_mid_peak_usage+history1.last_mid_peak_usage AS mid_usage ,
			history1.first_off_peak_usage+history1.last_off_peak_usage AS off_peak_usage
			FROM {$query_level_1}
			GROUP BY `year`,`month`,date_of_month,hour_of_day,is_summer) AS history2" ;

		$query_level_3 = "	(SELECT *,
			CASE WHEN is_summer = 1 THEN TRUNCATE(peak_usage*{$pricing->summer->peak},3) ELSE 0 END AS summer_peak_usage_price,
			CASE WHEN is_summer = 1 THEN TRUNCATE(peak_usage,3) ELSE 0 END AS summer_peak_usage,
			CASE WHEN is_summer = 1 THEN TRUNCATE(mid_usage*{$pricing->summer->mid},3) ELSE 0 END AS summer_mid_usage_price,
			CASE WHEN is_summer = 1 THEN TRUNCATE(mid_usage,3) ELSE 0 END AS summer_mid_usage,
			CASE WHEN is_summer = 1 THEN TRUNCATE(off_peak_usage*{$pricing->summer->off_peak},3) ELSE 0 END AS summer_off_peak_usage_price,
			CASE WHEN is_summer = 1 THEN TRUNCATE(off_peak_usage,3) ELSE 0 END AS summer_off_peak_usage,
			CASE WHEN is_summer = 0 THEN TRUNCATE(peak_usage*{$pricing->not_summer->peak},3) ELSE 0 END AS not_summer_peak_usage_price,
			CASE WHEN is_summer = 0 THEN TRUNCATE(peak_usage,3) ELSE 0 END AS not_summer_peak_usage,
			CASE WHEN is_summer = 0 THEN TRUNCATE(mid_usage*{$pricing->not_summer->mid},3) ELSE 0 END AS not_summer_mid_usage_price,
			CASE WHEN is_summer = 0 THEN TRUNCATE(mid_usage,3) ELSE 0 END AS not_summer_mid_usage,
			CASE WHEN is_summer = 0 THEN TRUNCATE(off_peak_usage*{$pricing->not_summer->off_peak},3) ELSE 0 END AS not_summer_off_peak_usage_price,
			CASE WHEN is_summer = 0 THEN TRUNCATE(off_peak_usage,3) ELSE 0 END AS not_summer_off_peak_usage
			FROM {$query_level_2}) AS history3 ";

		$query_level_4 ="SELECT
			`year`,
			`month`,
			date_of_month,
			hour_of_day,
			sum(summer_peak_usage) as summer_peak_usage,
			sum(summer_mid_usage) as summer_mid_usage,
			sum(summer_off_peak_usage) as summer_off_peak_usage,
			sum(not_summer_peak_usage) as not_summer_peak_usage,
			sum(not_summer_mid_usage) as not_summer_mid_usage,
			sum(not_summer_off_peak_usage) as not_summer_off_peak_usage,
			sum(summer_peak_usage_price) as summer_peak_usage_price,
			sum(summer_mid_usage_price) as summer_mid_usage_price,
			sum(summer_off_peak_usage_price) as summer_off_peak_usage_price,
			sum(not_summer_peak_usage_price) as not_summer_peak_usage_price,
			sum(not_summer_mid_usage_price) as not_summer_mid_usage_price,
			sum(not_summer_off_peak_usage_price) as not_summer_off_peak_usage_price,
			sum(summer_peak_usage) + sum(summer_mid_usage) + sum(summer_off_peak_usage) + sum(not_summer_peak_usage)  + sum(not_summer_mid_usage) + sum(not_summer_off_peak_usage) AS total_usage,
			sum(summer_peak_usage_price) + sum(summer_mid_usage_price) + sum(summer_off_peak_usage_price) + sum(not_summer_peak_usage_price)  + sum(not_summer_mid_usage_price) + sum(not_summer_off_peak_usage_price) AS total_price
			FROM {$query_level_3}
			GROUP BY  {$group_by_string}";

		// $sql = "SET @summer_peak_price:= {$pricing->summer->peak};
		// 		SET @summer_mid_price:= {$pricing->summer->mid};
		// 		SET @summer_off_peak_price:={$pricing->summer->off_peak};
		// 		SET @not_summer_peak_price:={$pricing->summer->peak};
		// 		SET @not_summer_mid_price:={$pricing->not_summer->mid};
		// 		SET @not_summer_off_peak_price:={$pricing->not_summer->off_peak};
		// 		SELECT * FROM {$query_level_3}
		// 		GROUP BY {$group_by_string}
		// 	";
		// $sql = "
		// 		SELECT * FROM {$query_level_3}
		// 		GROUP BY {$group_by_string}
		// 	";
		$db = JFactory::getDbo();
		// $db->setQuery("
		// SET @wd = NULL;
		// SET @first_peak = NULL;
		// SET @first_mid = NULL;
		// SET @first_off_peak = NULL;
		// SET @last_peak = NULL;
		// SET @last_mid = NULL;
		// SET @last_off_peak = NULL;
		// SET @first_half_hour_peak= NULL;
		// SET @last_half_hour_peak= NULL;
		// ");
		// return $query_level_4;
		$db->setQuery($query_level_4);


		// return $query_level_4;
		// $query = $db->getQuery(true);
		// $db->setQuery("SELECT * FROM xwzms_electronic_meter_history as history LIMIT 10");
		// $db->setQuery($query);
		// return $db->query();
		return (array) $db->loadObjectList();
	}
	public static function get_last_hourly_electronic_meter_record($device_id, $year, $month = 1, $date_of_month = 1,$hour_of_day = 0)
	{
		$db = JFactory::getDbo();
		$target_year = $db->quote($year);
		$target_month = $db->quote($month);
		$target_date_of_month = $db->quote($date_of_month);
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatepower')
			->from('#__electronic_meter_history as history')
			->order('year desc')
			->order('month desc')
			->order('date_of_month desc')
			->order('hour_of_day desc')
			->where("history.device_id = {$db->quote($device_id)}
			 and (history.year < {$target_year}
			 or (history.year = {$target_year} and history.month < {$target_month})
			 or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month < {$target_date_of_month})
			 or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month = {$target_date_of_month} and history.hour_of_day < {$hour_of_day}))
			 "
			 )
			->setLimit(1);
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_last_electronic_meter_record($device_id, $year, $month = 1, $date_of_month = 1)
	{
		$db = JFactory::getDbo();
		$target_year = $db->quote($year);
		$target_month = $db->quote($month);
		$target_date_of_month = $db->quote($date_of_month);
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatepower')
			->from('#__electronic_meter_history as history')
			->order('year desc')
			->order('month desc')
			->order('date_of_month desc')
			->order('hour_of_day desc')
			->where("history.device_id = {$db->quote($device_id)}
			 and (history.year < {$target_year}
			 or (history.year = {$target_year} and history.month < {$target_month})
			 or (history.year = {$target_year} and history.month = {$target_month} and history.date_of_month < {$target_date_of_month}))"
			 )
			->setLimit(1);
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_electronic_meter_info($device_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('*')
			->from('#__device_table as device')
			->where('device.id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$result = (array) $db->loadObjectList();
		if (count($result) > 0)
		{
			return $result[0];
		}
		return new StdClass();
	}
	public static function get_hourly_electronic_meter_statistics_by_period($device_id, $year, $month, $date_of_month)
	{

		$db = JFactory::getDbo();
		$as_weekday = "@wd := WEEKDAY(CONCAT(history.`year`,'-',history.`month`,'-',history.date_of_month))+1 as wd";
		$is_summer = "`month` >=6 && `month` <=9 AS is_summer";
		$first_half_peak_flag = "@first_peak :=(@wd > 0 && @wd <6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_peak_flag";

		$first_half_mid_peak_flag = "@first_mid :=(@wd = 6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_mid_flag";

		$first_half_off_peak_flag = "@first_off_peak := @first_peak = 0 && @first_mid = 0 AS first_half_off_peak_flag";

		$first_half_hour_peak = "(@first_off_peak *2 + @first_mid) AS first_half_hour_peak";


		$last_half_peak_flag = "@last_peak :=(@wd > 0 && @wd <6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_peak_flag";

		$last_half_mid_peak_flag = "@last_mid :=(@wd = 6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_mid_flag";

		$last_half_off_peak_flag = "@last_off_peak := @last_peak = 0 && @last_mid = 0 AS last_half_off_peak_flag";

		$last_half_hour_peak = "(@last_off_peak *2 + @last_mid) AS last_half_hour_peak";

		$query = $db->getQuery(true)
			->from('#__electronic_meter_history as history')
			->select(array(
				'device_id','`year`','`month`',	'`date_of_month`', 'hour_of_day',
				'accumulatepower', 'update_time',
				'first_half_hour_accmulatepower', 'first_half_hour_update_time',
				'last_half_hour_accmulatepower', 'last_half_hour_update_time',
				$is_summer,$as_weekday, $first_half_peak_flag,$first_half_mid_peak_flag,$first_half_off_peak_flag,$last_half_peak_flag,$last_half_mid_peak_flag,$last_half_off_peak_flag,$first_half_hour_peak,$last_half_hour_peak))
			// ->select($as_weekday)
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day'))
			->where('history.device_id = ' . $db->quote($device_id))
			->where('history.year = ' . $db->quote($year))
			->where('history.month = ' . $db->quote($month))
			->where('history.date_of_month = ' . $db->quote($date_of_month));
			// return $query->dump();
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_daily_electronic_meter_statistics_by_period($device_id, $year, $month)
	{
		$as_weekday = "@wd := WEEKDAY(CONCAT(history.`year`,'-',history.`month`,'-',history.date_of_month))+1 as wd";
		$is_summer = "`month` >=6 && `month` <=9 AS is_summer";
		$first_half_peak_flag = "@first_peak :=(@wd > 0 && @wd <6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_peak_flag";

		$first_half_mid_peak_flag = "@first_mid :=(@wd = 6 && (hour_of_day > 7 && hour_of_day < 23)) AS first_half_mid_flag";

		$first_half_off_peak_flag = "@first_off_peak := @first_peak = 0 && @first_mid = 0 AS first_half_off_peak_flag";

		$first_half_hour_peak = "@first_half_hour_peak: =(@first_off_peak *2 + @first_mid) AS first_half_hour_peak";


		$last_half_peak_flag = "@last_peak :=(@wd > 0 && @wd <6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_peak_flag";

		$last_half_mid_peak_flag = "@last_mid :=(@wd = 6 && (hour_of_day >= 7 && hour_of_day < 22)) AS last_half_mid_flag";

		$last_half_off_peak_flag = "@last_off_peak := @last_peak = 0 && @last_mid = 0 AS last_half_off_peak_flag";

		$last_half_hour_peak = "@last_half_hour_peak:= (@last_off_peak *2 + @last_mid) AS last_half_hour_peak";

		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select(
				'device_id, `year`,`month`,`date_of_month`, max(accumulatepower) AS maxAccumulatepower, MIN(accumulatepower) AS minaccumulatepower, CAST(max(accumulatepower)-MIN(accumulatepower) as DECIMAL(10,2)) AS diff',
				'first_half_hour_accmulatepower', 'first_half_hour_update_time',
				'last_half_hour_accmulatepower', 'last_half_hour_update_time',
				$is_summer,$as_weekday, $first_half_peak_flag,$first_half_mid_peak_flag,$first_half_off_peak_flag,$last_half_peak_flag,$last_half_mid_peak_flag,$last_half_off_peak_flag,$first_half_hour_peak,$last_half_hour_peak)
			->from('#__electronic_meter_history as electronic_meter_history')
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year`,`month`,`date_of_month` asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`','is_summer','first_half_hour_peak','last_half_hour_peak'))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->where('electronic_meter_history.year = ' . $db->quote($year))
			->where('electronic_meter_history.month = ' . $db->quote($month));
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_hourly_electronic_meter_statistics($device_id, $year, $month, $date_of_month)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, hour_of_day, accumulatepower, update_time')
			->from('#__electronic_meter_history as electronic_meter_history')
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`, hour_of_day'))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->where('electronic_meter_history.year = ' . $db->quote($year))
			->where('electronic_meter_history.month = ' . $db->quote($month))
			->where('electronic_meter_history.date_of_month = ' . $db->quote($date_of_month));
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_daily_electronic_meter_statistics($device_id, $year, $month)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`,`date_of_month`, max(accumulatepower) AS maxAccumulatepower, MIN(accumulatepower) AS minaccumulatepower, CAST(max(accumulatepower)-MIN(accumulatepower) as DECIMAL(10,2)) AS diff')
			->from('#__electronic_meter_history as electronic_meter_history')
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year`,`month`,`date_of_month` asc'))
			->group($db->escape('device_id, `year`,`month`,`date_of_month`'))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->where('electronic_meter_history.year = ' . $db->quote($year))
			->where('electronic_meter_history.month = ' . $db->quote($month));
		$db->setQuery($query);
		return (array) $db->loadObjectList();
	}
	public static function get_monthly_electronic_meter_statistics($device_id, $year)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`,`month`, max(accumulatepower) AS maxAccumulatepower, MIN(accumulatepower) AS minaccumulatepower, CAST(max(accumulatepower)-MIN(accumulatepower) as DECIMAL(10,2)) AS diff')
			->from('#__electronic_meter_history as electronic_meter_history')
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year`,`month` asc'))
			->group($db->escape('device_id, `year`,`month`'))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->where('electronic_meter_history.year = ' . $db->quote($year));
		$db->setQuery($query);

		$result = (array) $db->loadObjectList();


		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__electronic_meter_history as electronic_meter_history")
		// ->group($db->escape('month'))
		// ->order($db->escape('month desc'))
		// ->order($db->escape('date_of_month desc'))
		// ->order($db->escape('hour_of_day desc'))
		// ->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
		// ->where('electronic_meter_history.year = ' . $db->quote($year));
		// $db->setQuery($query);
		// $maxs = (array) $db->loadObjectList();

		// $query = $db->getQuery(true)
		// ->select('*')
		// ->from("#__electronic_meter_history as electronic_meter_history")
		// ->group($db->escape('month'))
		// ->order($db->escape('month asc'))
		// ->order($db->escape('date_of_month asc'))
		// ->order($db->escape('hour_of_day asc'))
		// ->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
		// ->where('electronic_meter_history.year = ' . $db->quote($year));
		// $db->setQuery($query);
		// $mins = (array) $db->loadObjectList();


		foreach ($result as $key => $value) {
			$result[$key]->maxAccumulatepower = 0;
			$result[$key]->minAccumulatepower = 0;
			$query = $db->getQuery(true)
			->select('*')
			->from("#__electronic_meter_history as electronic_meter_history")
			->order($db->escape('month desc'))
			->order($db->escape('date_of_month desc'))
			->order($db->escape('hour_of_day desc'))
			->where('year = ' . $db->quote($year))
			->where('month = ' . $db->quote($value->month))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$max = (array) $db->loadObjectList();
			if (count($max) > 0)
			{
				$result[$key]->maxAccumulatepower = $max[0]->accumulatepower;
			}

			$query = $db->getQuery(true)
			->select('*')
			->from("#__electronic_meter_history as electronic_meter_history")
			->order($db->escape('month asc'))
			->order($db->escape('date_of_month asc'))
			->order($db->escape('hour_of_day asc'))
			->where('year = ' . $db->quote($year))
			->where('month = ' . $db->quote($value->month))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$min = (array) $db->loadObjectList();
			if (count($min) > 0)
			{
				$result[$key]->minAccumulatepower = $min[0]->accumulatepower;
			}
			// foreach ($maxs as $key1 => $value1) {
			// 	if ($value1->month == $value->month)
			// 	{
			// 		$result[$key]->maxAccumulatepower = $value1->accumulatepower;
			// 	}
			// }
			// foreach ($mins as $key1 => $value1) {
			// 	if ($value1->month == $value->month)
			// 	{
			// 		$result[$key]->minAccumulatepower = $value1->accumulatepower;
			// 	}
			// }
			$result[$key]->diff = $result[$key]->maxAccumulatepower - $result[$key]->minAccumulatepower;
		}
		return $result;
	}
	public static function get_annual_electronic_meter_statistics($device_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
			->select('device_id, `year`')
			// ->select('id')
			->from('#__electronic_meter_history as electronic_meter_history')
			// ->order($db->escape('id DESC'))
			->order($db->escape('device_id, `year` asc'))
			->group($db->escape('device_id, `year`'))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$result = (array) $db->loadObjectList();

		// $query = $db->getQuery(true)
		// ->select('DISTINCT year')
		// ->from("#__electronic_meter_history as electronic_meter_history")
		// ->where('electronic_meter_history.device_id = ' . $db->quote($device_id));
		// $db->setQuery($query);
		// $years = (array) $db->loadObjectList();

		$query = $db->getQuery(true)
		->select('*')
		->from("#__electronic_meter_history as electronic_meter_history")
		->group($db->escape('year'))
		->order($db->escape('year desc'))
		->order($db->escape('month desc'))
		->order($db->escape('date_of_month desc'))
		->order($db->escape('hour_of_day desc'))
		->where('electronic_meter_history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$maxs = (array) $db->loadObjectList();

		$query = $db->getQuery(true)
		->select('*')
		->from("#__electronic_meter_history as electronic_meter_history")
		->group($db->escape('year'))
		->order($db->escape('year asc'))
		->order($db->escape('month asc'))
		->order($db->escape('date_of_month asc'))
		->order($db->escape('hour_of_day asc'))
		->where('electronic_meter_history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$mins = (array) $db->loadObjectList();


		foreach ($result as $key => $value) {
			$result[$key]->maxAccumulatepower = 0;
			$result[$key]->minAccumulatepower = 0;


			$query = $db->getQuery(true)
			->select('*')
			->from("#__electronic_meter_history as electronic_meter_history")
			->order($db->escape('year desc'))
			->order($db->escape('month desc'))
			->order($db->escape('date_of_month desc'))
			->order($db->escape('hour_of_day desc'))
			->where('year = ' . $db->quote($value->year))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$max = (array) $db->loadObjectList();
			if (count($max) > 0)
			{
				$result[$key]->maxAccumulatepower = $max[0]->accumulatepower;
			}

			$query = $db->getQuery(true)
			->select('*')
			->from("#__electronic_meter_history as electronic_meter_history")
			->order($db->escape('year asc'))
			->order($db->escape('month asc'))
			->order($db->escape('date_of_month asc'))
			->order($db->escape('hour_of_day asc'))
			->where('year = ' . $db->quote($value->year))
			->where('electronic_meter_history.device_id = ' . $db->quote($device_id))
			->setLimit(1);
			$db->setQuery($query);
			$min = (array) $db->loadObjectList();
			if (count($min) > 0)
			{
				$result[$key]->minAccumulatepower = $min[0]->accumulatepower;
			}
			// foreach ($maxs as $key1 => $value1) {
			// 	if ($value1->year == $value->year)
			// 	{
			// 		$result[$key]->maxAccumulatepower = $value1->accumulatepower;
			// 	}
			// }
			// foreach ($mins as $key1 => $value1) {
			// 	if ($value1->year == $value->year)
			// 	{
			// 		$result[$key]->minAccumulatepower = $value1->accumulatepower;
			// 	}
			// }
			$result[$key]->diff = $result[$key]->maxAccumulatepower - $result[$key]->minAccumulatepower;
		}
		return $result;


	}
	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

	/**
	 * Gets the edit permission for an user
	 *
	 * @param   mixed  $item  The item
	 *
	 * @return  bool
	 */
	public static function canUserEdit($item)
	{
		$permission = false;
		$user       = Factory::getUser();

		if ($user->authorise('core.edit', 'com_electronic_meter_history')) {
			$permission = true;
		} else {
			if (isset($item->created_by)) {
				if ($user->authorise('core.edit.own', 'com_electronic_meter_history') && $item->created_by == $user->id) {
					$permission = true;
				}
			} else {
				$permission = true;
			}
		}

		return $permission;
	}
}
