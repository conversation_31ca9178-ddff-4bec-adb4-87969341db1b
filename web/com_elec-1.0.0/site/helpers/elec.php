<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Elec
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2020 kevin lo
 * @license    GNU 通用公共许可版本 2 或更高版本 ；请参阅 LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('El<PERSON><PERSON>el<PERSON>', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_elec' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'elec.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

/**
 * Class ElecFrontendHelper
 *
 * @since  1.6
 */
class ElecHelpersElec
{
	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */

	 public static $year_type;
	 public static $season_type;
	 public static $week_type;
	 public static $month_type;
	 public static $day_type;
	 public static $hour_type;
	 public static $dio_weema_485_vendor;

   public static $elec_device;
   public static $elec_device_cic;
   public static $elec_device_vmr_mp7;
   public static $elec_device_vmr_mp8;
   public static $elec_device_daepm210;
   public static $elec_device_m4m;
   public static $elec_device_kt_mk3;
   public static $elec_device_baw4c;
   public static $elec_device_acuvim;
   public static $elec_device_tatung;
   public static $elec_device_pem333;
   public static $elec_device_pem575;
   public static $elec_device_shihlin;
   public static $elec_device_cicbaw1a2a;
   public static $elec_device_cicbaw2c;
   public static $elec_device_weema_1p;
   public static $elec_device_weema_3p;
   public static $elec_device_opcda;
   public static $elec_device_general_opcda;
   public static $elec_device_aem_drb;
   public static $solar_elec_device_primevolt;
   public static $solar_elec_device_general;
   public static $solar_elec_device_shihlinspm8;
	 public static $dpage;
   public static $ddiopage;

	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_elec/models/' . strtolower($name) . '.php'))
		{
			require_once JPATH_SITE . '/components/com_elec/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'ElecModel');
		}

		return $model;
	}

	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

    /**
     * Gets the edit permission for an user
     *
     * @param   mixed  $item  The item
     *
     * @return  bool
     */
    public static function canUserEdit($item)
    {
        $permission = false;
        $user       = Factory::getUser();

        if ($user->authorise('core.edit', 'com_elec'))
        {
            $permission = true;
        }
        else
        {
            if (isset($item->created_by))
            {
                if ($user->authorise('core.edit.own', 'com_elec') && $item->created_by == $user->id)
                {
                    $permission = true;
                }
            }
            else
            {
                $permission = true;
            }
        }

        return $permission;
    }

		public static function getElecDirs()
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$ddiopage);

	    $query->where('a.vendor = '.self::$dio_weema_485_vendor);

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
	     //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

    }

		public static function getElecDir($top_id)
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');

			// $query->where('a.dio_type = '.self::$elec_device );
			$query->where('a.dio_type IN ('.implode(',',$db->quote(
				array(
					self::$elec_device,
					self::$elec_device_cic,
					self::$elec_device_vmr_mp7,
					self::$elec_device_vmr_mp8,
					self::$elec_device_daepm210,
					self::$elec_device_m4m,
					self::$elec_device_kt_mk3,
					self::$elec_device_baw4c,
					self::$elec_device_tatung,
					self::$elec_device_acuvim))).")");

			$query->where('a.note1 = '.self::$dpage );

	 	  $query->where($db->quoteName('a.parent') . ' = ' . $db->quote($top_id));

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
	     //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

    }
		public static function getDevice()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

			 $query->where('a.dio_id > 0');
			//  $query->where('a.dio_type = '.self::$elec_device);
			//  $query->where('a.dio_type IN '.$db->quote(array(self::$elec_device,self::$elec_device_cic)));
			$query->where('a.dio_type IN ('.implode(',',$db->quote(
				array(
					self::$elec_device,
					self::$elec_device_cic,
					self::$elec_device_vmr_mp7,
					self::$elec_device_vmr_mp8,
					self::$elec_device_daepm210,
					self::$elec_device_m4m,
					self::$elec_device_kt_mk3,
					self::$elec_device_baw4c,
					self::$elec_device_acuvim,
					self::$elec_device_tatung,
					self::$elec_device_pem333,
					self::$elec_device_pem575,
					self::$elec_device_shihlin,
					self::$elec_device_cicbaw1a2a,
					self::$elec_device_opcda,
					self::$elec_device_general_opcda,
					self::$elec_device_aem_drb,
					self::$solar_elec_device_primevolt,
					self::$solar_elec_device_general,
					self::$solar_elec_device_shihlinspm8,
					self::$elec_device_cicbaw2c,
					self::$elec_device_weema_1p,
					self::$elec_device_weema_3p
					))).")");
		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}


}

ElecHelpersElec::$year_type=2;
ElecHelpersElec::$season_type=3;
ElecHelpersElec::$week_type=4;
ElecHelpersElec::$month_type=5;
ElecHelpersElec::$day_type=6;
ElecHelpersElec::$hour_type=7;

ElecHelpersElec::$elec_device=5;
ElecHelpersElec::$elec_device_cic=9;
ElecHelpersElec::$elec_device_daepm210=44;
ElecHelpersElec::$elec_device_m4m=50;
ElecHelpersElec::$elec_device_kt_mk3=51;
ElecHelpersElec::$elec_device_baw4c=52;
ElecHelpersElec::$elec_device_acuvim=43;
ElecHelpersElec::$elec_device_tatung=10;
ElecHelpersElec::$elec_device_pem333=12;
ElecHelpersElec::$elec_device_pem575=13;
ElecHelpersElec::$elec_device_shihlin=14;
ElecHelpersElec::$elec_device_opcda=23;
ElecHelpersElec::$elec_device_general_opcda=31;
ElecHelpersElec::$elec_device_aem_drb=29;
ElecHelpersElec::$elec_device_weema_1p=24;
ElecHelpersElec::$elec_device_weema_3p=25;
ElecHelpersElec::$elec_device_vmr_mp7=21;
ElecHelpersElec::$elec_device_vmr_mp8=45;
ElecHelpersElec::$elec_device_cicbaw1a2a=36;
ElecHelpersElec::$elec_device_cicbaw2c=47;
ElecHelpersElec::$solar_elec_device_primevolt=19;
ElecHelpersElec::$solar_elec_device_general=37;
ElecHelpersElec::$solar_elec_device_shihlinspm8=33;

ElecHelpersElec::$dio_weema_485_vendor = 6;
ElecHelpersElec::$dpage = 3;
ElecHelpersElec::$ddiopage = 14;

