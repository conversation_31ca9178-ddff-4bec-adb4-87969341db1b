<?xml version="1.0" encoding="utf-8"?>
<extension type="component" version="3.0" method="upgrade">
    <name>com_safety_message</name>
    <creationDate>2024-11-28</creationDate>
    <copyright>2021 <PERSON></copyright>
    <license>GNU General Public License version 2 or later; see LICENSE.txt</license>
    <author><PERSON></author>
    <authorEmail><EMAIL></authorEmail>
    <authorUrl>http://</authorUrl>
    <version>CVS: 1.0.0</version>
    <description></description>
    
    
    
    <install> <!-- Runs on install -->
        <sql>
            <file driver="mysql" charset="utf8">sql/install.mysql.utf8.sql</file>
        </sql>
    </install>
    <update> <!-- Runs on update -->
        <schemas>
            <schemapath type="mysql">sql/updates</schemapath>
        </schemas>
    </update>
    <uninstall> <!-- Runs on uninstall -->
        <sql>
            <file driver="mysql" charset="utf8">sql/uninstall.mysql.utf8.sql</file>
        </sql>
    </uninstall>

    <files folder="site">
        <filename>index.html</filename>
        <filename>safety_message.php</filename>
        <filename>controller.php</filename>
        <filename>router.php</filename>
        <folder>views</folder>
        <folder>models</folder>
        <folder>controllers</folder>
        <folder>helpers</folder>
    </files>
    <media destination="com_safety_message" folder="media">
        <folder>js</folder>
        <folder>css</folder>
    </media>
    <languages folder="site/languages">
        
			<language tag="zh-TW">zh-TW/zh-TW.com_safety_message.ini</language>
			<language tag="en-GB">en-GB/en-GB.com_safety_message.ini</language>
    </languages>
    <administration>
        <menu>COM_SAFETY_MESSAGE</menu>
        <submenu>
            
			<menu link="option=com_safety_message&amp;view=safetymessages" view="safetymessages" alt="Safety_message/Safetymessages">COM_SAFETY_MESSAGE_TITLE_SAFETYMESSAGES</menu>

        </submenu>
        <files folder="administrator">
            <filename>access.xml</filename>
            <filename>config.xml</filename>
            <filename>controller.php</filename>
            <filename>index.html</filename>
            <filename>safety_message.php</filename>
            <folder>controllers</folder>
            <folder>assets</folder>
            <folder>helpers</folder>
            <folder>models</folder>
            <folder>sql</folder>
            <folder>tables</folder>
            <folder>views</folder>
        </files>
        <languages folder="administrator/languages">
            
			<language tag="zh-TW">zh-TW/zh-TW.com_safety_message.ini</language>
			<language tag="zh-TW">zh-TW/zh-TW.com_safety_message.sys.ini</language>
			<language tag="en-GB">en-GB/en-GB.com_safety_message.ini</language>
			<language tag="en-GB">en-GB/en-GB.com_safety_message.sys.ini</language>
        </languages>
    </administration>
    <config>
        <fields name="params">
            <fieldset name="component">
                <field name="save_history" default="0" />
            </fieldset>
        </fields>
    </config>
    <plugins>
        <plugin group="search" plugin="safety_message"/>
    </plugins>
    
</extension>
<!-- Component built by the Joomla Component Creator -->
<!-- http://www.component-creator.com/ -->
