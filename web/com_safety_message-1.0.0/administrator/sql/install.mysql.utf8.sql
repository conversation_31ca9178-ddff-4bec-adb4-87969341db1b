CREATE TABLE IF NOT EXISTS `#__safetymessage` (
`id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,

`ordering` INT(11)  NOT NULL  DEFAULT 0,
`state` TINYINT(1)  NOT NULL  DEFAULT 1,
`checked_out` INT(11)  NOT NULL  DEFAULT 0,
`checked_out_time` DATETIME NOT NULL  DEFAULT "0000-00-00 00:00:00",
`created_by` INT(11)  NOT NULL  DEFAULT 0,
`modified_by` INT(11)  NOT NULL  DEFAULT 0,
`create_time` DATETIME NOT NULL  DEFAULT "0000-00-00 00:00:00",
`card_id` VARCHAR(255)  NOT NULL ,
`cam_id` VARCHAR(255)  NOT NULL ,
`display_name` VARCHAR(255)  NOT NULL ,
`sms_receivers` VARCHAR(255)  NOT NULL  DEFAULT "",
`email_receivers` VARCHAR(255)  NOT NULL  DEFAULT "",
`door_id` VARCHAR(255)  NOT NULL ,
<PERSON><PERSON>AR<PERSON>EY (`id`)
,<PERSON>EY `idx_state` (`state`)
,<PERSON><PERSON>Y `idx_checked_out` (`checked_out`)
,KEY `idx_created_by` (`created_by`)
,KEY `idx_modified_by` (`modified_by`)
) DEFAULT COLLATE=utf8mb4_unicode_ci;

