<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Safety_message
 * <AUTHOR> <<EMAIL>>
 * @copyright  2021 Hu <PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;
use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;
use \Joomla\CMS\Layout\LayoutHelper;

$canEdit = JFactory::getUser()->authorise('core.edit', 'com_safety_message');

if (!$canEdit && JFactory::getUser()->authorise('core.edit.own', 'com_safety_message'))
{
	$canEdit = JFactory::getUser()->id == $this->item->created_by;
}
?>

<div class="item_fields">

	<table class="table">
		

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_CARD_ID'); ?></th>
			<td><?php echo $this->item->card_id; ?></td>
		</tr>

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_CAM_ID'); ?></th>
			<td><?php echo $this->item->cam_id; ?></td>
		</tr>

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_DISPLAY_NAME'); ?></th>
			<td><?php echo $this->item->display_name; ?></td>
		</tr>

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_SMS_RECEIVERS'); ?></th>
			<td><?php echo $this->item->sms_receivers; ?></td>
		</tr>

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_EMAIL_RECEIVERS'); ?></th>
			<td><?php echo $this->item->email_receivers; ?></td>
		</tr>

		<tr>
			<th><?php echo Text::_('COM_SAFETY_MESSAGE_FORM_LBL_SAFETYMESSAGE_DOOR_ID'); ?></th>
			<td><?php echo $this->item->door_id; ?></td>
		</tr>

	</table>

</div>

<?php if($canEdit && $this->item->checked_out == 0): ?>

	<a class="btn" href="<?php echo JRoute::_('index.php?option=com_safety_message&task=safetymessage.edit&id='.$this->item->id); ?>"><?php echo Text::_("COM_SAFETY_MESSAGE_EDIT_ITEM"); ?></a>

<?php endif; ?>

<?php if (JFactory::getUser()->authorise('core.delete','com_safety_message.safetymessage.'.$this->item->id)) : ?>

	<a class="btn btn-danger" href="#deleteModal" role="button" data-toggle="modal">
		<?php echo Text::_("COM_SAFETY_MESSAGE_DELETE_ITEM"); ?>
	</a>

	<div id="deleteModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="deleteModal" aria-hidden="true">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			<h3><?php echo Text::_('COM_SAFETY_MESSAGE_DELETE_ITEM'); ?></h3>
		</div>
		<div class="modal-body">
			<p><?php echo Text::sprintf('COM_SAFETY_MESSAGE_DELETE_CONFIRM', $this->item->id); ?></p>
		</div>
		<div class="modal-footer">
			<button class="btn" data-dismiss="modal">Close</button>
			<a href="<?php echo JRoute::_('index.php?option=com_safety_message&task=safetymessage.remove&id=' . $this->item->id, false, 2); ?>" class="btn btn-danger">
				<?php echo Text::_('COM_SAFETY_MESSAGE_DELETE_ITEM'); ?>
			</a>
		</div>
	</div>

<?php endif; ?>