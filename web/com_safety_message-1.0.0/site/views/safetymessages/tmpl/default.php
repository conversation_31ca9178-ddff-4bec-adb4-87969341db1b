<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Safety_message
 * <AUTHOR> <<EMAIL>>
 * @copyright  2021 Hu <PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;
use \Joomla\CMS\Layout\LayoutHelper;

HTMLHelper::addIncludePath(JPATH_COMPONENT . '/helpers/html');
HTMLHelper::_('bootstrap.tooltip');
HTMLHelper::_('behavior.multiselect');
HTMLHelper::_('formbehavior.chosen', 'select');

$user       = Factory::getUser();
$userId     = $user->get('id');
$listOrder  = $this->state->get('list.ordering');
$listDirn   = $this->state->get('list.direction');
$canCreate  = $user->authorise('core.create', 'com_safety_message') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'safetymessageform.xml');
$canEdit    = $user->authorise('core.edit', 'com_safety_message') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'safetymessageform.xml');
$canCheckin = $user->authorise('core.manage', 'com_safety_message');
$canChange  = $user->authorise('core.edit.state', 'com_safety_message');
$canDelete  = $user->authorise('core.delete', 'com_safety_message');

// Import CSS
$document = Factory::getDocument();
$document->addStyleSheet(Uri::root() . 'media/com_safety_message/css/list.css');
?>

<form action="<?php echo htmlspecialchars(Uri::getInstance()->toString()); ?>" method="post"
      name="adminForm" id="adminForm">

	<?php echo JLayoutHelper::render('default_filter', array('view' => $this), dirname(__FILE__)); ?>
        <div class="table-responsive">
	<table class="table table-striped" id="safetymessageList">
		<thead>
		<tr>

							<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_ID', 'a.id', $listDirn, $listOrder); ?>
				</th>
				<th >
					<?php echo JHtml::_('grid.sort', 'JPUBLISHED', 'a.state', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_CARD_ID', 'a.card_id', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_CAM_ID', 'a.cam_id', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_DOOR_ID', 'a.door_id', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_DISPLAY_NAME', 'a.display_name', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_SMS_RECEIVERS', 'a.sms_receivers', $listDirn, $listOrder); ?>
				</th>
				<th class=''>
				<?php echo JHtml::_('grid.sort',  'COM_SAFETY_MESSAGE_SAFETYMESSAGES_EMAIL_RECEIVERS', 'a.email_receivers', $listDirn, $listOrder); ?>
				</th>


				<?php if ($canEdit || $canDelete): ?>
				<th class="center">
					<?php echo JText::_('COM_SAFETY_MESSAGE_SAFETYMESSAGES_ACTIONS'); ?>
				</th>
				<?php endif; ?>

		</tr>
		</thead>
		<tfoot>
		<tr>
			<td colspan="<?php echo isset($this->items[0]) ? count(get_object_vars($this->items[0])) : 10; ?>">
				<div class="pagination">
					<?php echo $this->pagination->getPagesLinks(); ?>
				</div>
			</td>
		</tr>
		</tfoot>
		<tbody>
		<?php foreach ($this->items as $i => $item) : ?>
			<?php $canEdit = $user->authorise('core.edit', 'com_safety_message'); ?>

							<?php if (!$canEdit && $user->authorise('core.edit.own', 'com_safety_message')): ?>
					<?php $canEdit = JFactory::getUser()->id == $item->created_by; ?>
				<?php endif; ?>

			<tr class="row<?php echo $i % 2; ?>">

				
				<td>

					<?php echo $item->id; ?>
				</td>
				<td>

					<?php $class = ($canChange) ? 'active' : 'disabled'; ?>
					<a class="btn btn-micro <?php echo $class; ?>" href="<?php echo ($canChange) ? JRoute::_('index.php?option=com_safety_message&task=safetymessage.publish&id=' . $item->id . '&state=' . (($item->state + 1) % 2), false, 2) : '#'; ?>">
					<?php if ($item->state == 1): ?>
						<i class="icon-publish"></i>
					<?php else: ?>
						<i class="icon-unpublish"></i>
					<?php endif; ?>
					</a>				</td>
				<td>
				<?php if (isset($item->checked_out) && $item->checked_out) : ?>
					<?php echo JHtml::_('jgrid.checkedout', $i, $item->uEditor, $item->checked_out_time, 'safetymessages.', $canCheckin); ?>
				<?php endif; ?>
				<a href="<?php echo JRoute::_('index.php?option=com_safety_message&view=safetymessage&id='.(int) $item->id); ?>">
							<?php echo $this->escape($item->card_id); ?></a>
				</td>
				<td>

					<?php echo $item->cam_id; ?>
				</td>
				<td>

					<?php echo $item->door_id; ?>
				</td>
				<td>

					<?php echo $item->display_name; ?>
				</td>
				<td>

					<?php echo $item->sms_receivers; ?>
				</td>
				<td>

					<?php echo $item->email_receivers; ?>
				</td>

				<?php if ($canEdit || $canDelete): ?>
					<td class="center">
						<?php if ($canEdit): ?>
							<a href="<?php echo JRoute::_('index.php?option=com_safety_message&task=safetymessage.edit&id=' . $item->id, false, 2); ?>" class="btn btn-mini" type="button"><i class="icon-edit" ></i></a>
						<?php endif; ?>
						<?php if ($canDelete): ?>
							<a href="<?php echo JRoute::_('index.php?option=com_safety_message&task=safetymessageform.remove&id=' . $item->id, false, 2); ?>" class="btn btn-mini delete-button" type="button"><i class="icon-trash" ></i></a>
						<?php endif; ?>
					</td>
				<?php endif; ?>

			</tr>
		<?php endforeach; ?>
		</tbody>
	</table>
        </div>
	<?php if ($canCreate) : ?>
		<a href="<?php echo Route::_('index.php?option=com_safety_message&task=safetymessageform.edit&id=0', false, 0); ?>"
		   class="btn btn-success btn-small"><i
				class="icon-plus"></i>
			<?php echo Text::_('COM_SAFETY_MESSAGE_ADD_ITEM'); ?></a>
	<?php endif; ?>

	<input type="hidden" name="task" value=""/>
	<input type="hidden" name="boxchecked" value="0"/>
	<input type="hidden" name="filter_order" value="<?php echo $listOrder; ?>"/>
	<input type="hidden" name="filter_order_Dir" value="<?php echo $listDirn; ?>"/>
	<?php echo HTMLHelper::_('form.token'); ?>
</form>

<?php if($canDelete) : ?>
<script type="text/javascript">

	jQuery(document).ready(function () {
		jQuery('.delete-button').click(deleteItem);
	});

	function deleteItem() {

		if (!confirm("<?php echo Text::_('COM_SAFETY_MESSAGE_DELETE_MESSAGE'); ?>")) {
			return false;
		}
	}
</script>
<?php endif; ?>
