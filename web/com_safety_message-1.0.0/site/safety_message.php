<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Safety_message
 * <AUTHOR> <<EMAIL>>
 * @copyright  2021 Hu <PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */

defined('_JEXEC') or die;

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Controller\BaseController;

// Include dependancies
jimport('joomla.application.component.controller');

JLoader::registerPrefix('Safety_message', JPATH_COMPONENT);
JLoader::register('Safety_messageController', JPATH_COMPONENT . '/controller.php');


// Execute the task.
$controller = BaseController::getInstance('Safety_message');
$controller->execute(Factory::getApplication()->input->get('task'));
$controller->redirect();
