<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Safety_message
 * <AUTHOR> <<EMAIL>>
 * @copyright  2021 Hu Yu<PERSON>
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use <PERSON><PERSON><PERSON>\CMS\Component\Router\RouterViewConfiguration;
use Joomla\CMS\Component\Router\RouterView;
use <PERSON><PERSON><PERSON>\CMS\Component\Router\Rules\StandardRules;
use Jo<PERSON><PERSON>\CMS\Component\Router\Rules\NomenuRules;
use Jo<PERSON><PERSON>\CMS\Component\Router\Rules\MenuRules;
use <PERSON><PERSON><PERSON>\CMS\Factory;
use Joomla\CMS\Categories\Categories;

/**
 * Class Safety_messageRouter
 *
 */
class Safety_messageRouter extends RouterView
{
	private $noIDs;
	public function __construct($app = null, $menu = null)
	{
		$params = JComponentHelper::getComponent('com_safety_message')->params;
		$this->noIDs = (bool) $params->get('sef_ids');
		
		
			$safetymessages = new RouterViewConfiguration('safetymessages');
		$this->registerView($safetymessages);
			$ccSafetymessage = new RouterViewConfiguration('safetymessage');
			$ccSafetymessage->setKey('id')->setParent($safetymessages);
			$this->registerView($ccSafetymessage);
			$safetymessageform = new RouterViewConfiguration('safetymessageform');
			$safetymessageform->setKey('id');
			$this->registerView($safetymessageform);

		parent::__construct($app, $menu);

		$this->attachRule(new MenuRules($this));

		if ($params->get('sef_advanced', 0))
		{
			$this->attachRule(new StandardRules($this));
			$this->attachRule(new NomenuRules($this));
		}
		else
		{
			JLoader::register('Safety_messageRulesLegacy', __DIR__ . '/helpers/legacyrouter.php');
			JLoader::register('Safety_messageHelpersSafety_message', __DIR__ . '/helpers/safety_message.php');
			$this->attachRule(new Safety_messageRulesLegacy($this));
		}
	}


	
		/**
		 * Method to get the segment(s) for an safetymessage
		 *
		 * @param   string  $id     ID of the safetymessage to retrieve the segments for
		 * @param   array   $query  The request that is built right now
		 *
		 * @return  array|string  The segments of this item
		 */
		public function getSafetymessageSegment($id, $query)
		{
			return array((int) $id => $id);
		}
			/**
			 * Method to get the segment(s) for an safetymessageform
			 *
			 * @param   string  $id     ID of the safetymessageform to retrieve the segments for
			 * @param   array   $query  The request that is built right now
			 *
			 * @return  array|string  The segments of this item
			 */
			public function getSafetymessageformSegment($id, $query)
			{
				return $this->getSafetymessageSegment($id, $query);
			}

	
		/**
		 * Method to get the segment(s) for an safetymessage
		 *
		 * @param   string  $segment  Segment of the safetymessage to retrieve the ID for
		 * @param   array   $query    The request that is parsed right now
		 *
		 * @return  mixed   The id of this item or false
		 */
		public function getSafetymessageId($segment, $query)
		{
			return (int) $segment;
		}
			/**
			 * Method to get the segment(s) for an safetymessageform
			 *
			 * @param   string  $segment  Segment of the safetymessageform to retrieve the ID for
			 * @param   array   $query    The request that is parsed right now
			 *
			 * @return  mixed   The id of this item or false
			 */
			public function getSafetymessageformId($segment, $query)
			{
				return $this->getSafetymessageId($segment, $query);
			}
}
