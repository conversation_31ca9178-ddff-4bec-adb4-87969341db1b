<?php
/**
 * API 資源：取得更新清單
 * 呼叫 TopUtility::GetUpdateList() 方法取得所有啟用的 APK 應用程式清單
 */
class UsersApiResourceGetupdatelist extends ApiResource
{
	/**
	 * GET 請求處理
	 * 取得所有啟用的 APK 應用程式清單
	 */
	public function get()
	{
		$result = new stdClass;
		
		// 呼叫 TopUtility::GetUpdateList() 方法
		$updateList = TopHelpersUtility::GetUpdateList();

		$result->json = $updateList;
		
		$this->plugin->setResponse( $result );
	}

	/**
	 * POST 請求處理
	 * 與 GET 請求相同的處理邏輯
	 */
	public function post()
	{
		// POST 請求使用與 GET 相同的邏輯
		return $this->get();
	}
}
