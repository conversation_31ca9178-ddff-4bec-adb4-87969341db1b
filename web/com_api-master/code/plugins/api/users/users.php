<?php
/**
 * @package     API
 * @subpackage  Authentication.tjtokenlogin
 *
 * @copyright   Copyright (C) 2009 - 2019 Techjoomla. All rights reserved.
 * @license     http://www.gnu.org/licenses/gpl-2.0.html GNU/GPL
 */

jimport('joomla.plugin.plugin');
JLoader::register('TopHelpersUtility', JPATH_SITE . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'toputility.php');

//class structure example
class plgAPIUsers extends ApiPlugin
{
	public function __construct(&$subject, $config = array())
	{
		parent::__construct($subject, $config = array());

		// Set resource path
		ApiResource::addIncludePath(dirname(__FILE__).'/users');

		// Load language files
		$lang = JFactory::getLanguage();
		$lang->load('com_users', JPATH_ADMINISTRATOR, '', true);

		// Set the login resource to be public
		$this->setResourceAccess('login', 'public', 'post');
		$this->setResourceAccess('login', 'public', 'get');

		$this->setResourceAccess('getstatus', 'public', 'post');
		$this->setResourceAccess('getstatus', 'public', 'get');		

		$this->setResourceAccess('getcctvdev', 'public', 'post');
		$this->setResourceAccess('getcctvdev', 'public', 'get');
		
		$this->setResourceAccess('getelec', 'public', 'post');
		$this->setResourceAccess('getelec', 'public', 'get');	
		
		$this->setResourceAccess('getlimitcall', 'public', 'post');
		$this->setResourceAccess('getlimitcall', 'public', 'get');		
		
		$this->setResourceAccess('getbc', 'public', 'post');
		$this->setResourceAccess('getbc', 'public', 'get');		
		
		$this->setResourceAccess('setbc', 'public', 'post');
		$this->setResourceAccess('setbc', 'public', 'get');		
		
		$this->setResourceAccess('getipcam', 'public', 'post');
		$this->setResourceAccess('getipcam', 'public', 'get');		

		$this->setResourceAccess('getfromphone', 'public', 'post');
		$this->setResourceAccess('getfromphone', 'public', 'get');				

		$this->setResourceAccess('update', 'public', 'post');
		$this->setResourceAccess('update', 'public', 'get');

		$this->setResourceAccess('getupdatelist', 'public', 'post');
		$this->setResourceAccess('getupdatelist', 'public', 'get');

		$this->setResourceAccess('sendgas', 'public', 'post');
		$this->setResourceAccess('sendgas', 'public', 'get');
		
		$this->setResourceAccess('getpublicip', 'public', 'post');
		$this->setResourceAccess('getpublicip', 'public', 'get');			

		$this->setResourceAccess('upload', 'public', 'post');
		$this->setResourceAccess('upload', 'public', 'get');			

	}
}
