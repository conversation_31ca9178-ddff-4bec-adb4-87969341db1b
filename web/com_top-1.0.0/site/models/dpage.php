<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Top
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2019 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */

defined('_JEXEC') or die;

use \Joomla\CMS\Factory;
use \Joomla\CMS\Language\Text;

jimport('joomla.application.component.modellist');

/**
 * Methods supporting a list of Top records.
 *
 * @since  1.6
 */
class TopModelDPage extends \Joomla\CMS\MVC\Model\ListModel
{
	/**
	 * Constructor.
	 *
	 * @param   array  $config  An optional associative array of configuration settings.
	 *
	 * @see        JController
	 * @since      1.6
	 */
	public function __construct($config = array())
	{
		if (empty($config['filter_fields']))
		{
			$config['filter_fields'] = array(
				'id', 'a.id',
				'ordering', 'a.ordering',
				'state', 'a.state',
				'created_by', 'a.created_by',
				'modified_by', 'a.modified_by',
				'myid', 'a.myid',
				'name', 'a.name',
				'note', 'a.note',
				'note1', 'a.note1',
				'note2', 'a.note2',
				'note3', 'a.note3',
				'note4', 'a.note4',
				'note5', 'a.note5',
				'username', 'a.`username`',
				'password', 'a.`password`',
				'serverip', 'a.`serverip`',
				'red', 'a.red',
				'yellow', 'a.yellow',
				'green', 'a.green',
				'animation', 'a.animation',
				'width', 'a.`width`',
        'height', 'a.`height`',
        'online', 'a.`online`',
        'offline', 'a.`offline`',
				'online_cnt', 'a.`online_cnt`',
        'offline_cnt', 'a.`offline_cnt`',
				'alarm', 'a.alarm',
				'type', 'a.type',
				'path', 'a.path',
				'nc', 'a.nc'
			);
		}

		parent::__construct($config);
	}

	protected function wa_populateState($ordering = null, $direction = null)
	{
		$app  = JFactory::getApplication();
		$myid = $app->input->getInt('id', 0);
    $this->setState('list_myid', $myid);

		$top_id = $app->input->getInt('top_id', 0);
    $this->setState('top_id', $top_id);

    $is_save = $app->input->getInt('is_save', 0);

    if($is_save == 1)
		{
			$this->save_item();
			TopHelpersToplog::action_loginlog(TopHelpersToplog::$dpage_change);
		}
  }


	protected function save_item()
  {
		$app  = Factory::getApplication();

		$id1 = $app->input->get('id1', array(), 'array');
		$index = $app->input->get('index', array(), 'array');
		$dio_type = $app->input->get('dio_type', array(), 'array');

		//$dio_value = $app->input->get('dio_value', array(), 'array');

		$note = $app->input->get('note', array(), 'array');
		$nc = $app->input->get('nc', array(), 'array');
		$supergroup = $app->input->get('supergroup', array(), 'array');
		$group = $app->input->get('group', array(), 'array');
		$top_floor = $app->input->get('top_floor', array(), 'array');
		$dio_alarmdir = $app->input->get('dio_alarmdir', array(), 'array');
		//$dio_alarm = $app->input->get('dio_alarm', array(), 'array');
		$device_enable = $app->input->get('device_enable', array(), 'array');
		
		$fontsize = $app->input->get('fontsize', array(), 'array');
		$color = $app->input->get('color', array(), 'array');
		
		$floors = $app->input->get('dio_floor', array(), 'array');
		$info = $app->input->get('info', array(), 'array');
		
		$id = $app->input->getInt('id', 0);
		$name = $app->input->getVar('name', '');
		$addr = $app->input->getVar('addr', '0001');
		$dio_type1 = $app->input->getVar('dio_type1', '1');
		$show_node_texts = $app->input->get('show_node_texts',array(),'array');
		$trigger_alarms = $app->input->get('trigger_alarms',array(),'array');
		$is_fault_nodes = $app->input->get('is_fault_nodes',array(),'array');
		$display_last_alarm_times = $app->input->get('display_last_alarm_times',array(),'array');
		$ba_app_enables = $app->input->get('ba_app_enables',array(),'array');
		//$dio_type = $app->input->getVar('dio_type', '');
		$alarm_thresholds = $app->input->get('alarm_threshold', array(), 'array');
		//$type = $app->input->getInt('type', 0);
		$temperature_alarm_thresholds = $app->input->get('temperature_alarm_threshold', array(), 'array');
		$temperature_alarmdirs = $app->input->get('temperature_alarmdir', array(), 'array');
		$temperature_alarms = $app->input->get('temperature_alarm', array(), 'array');

		$lux_alarm_thresholds = $app->input->get('lux_alarm_threshold', array(), 'array');
		$lux_alarmdirs = $app->input->get('lux_alarmdir', array(), 'array');
		$lux_alarms = $app->input->get('lux_alarm', array(), 'array');
		
		$humidity_alarm_thresholds = $app->input->get('humidity_alarm_threshold', array(), 'array');
		$humidity_alarmdirs = $app->input->get('humidity_alarmdir', array(), 'array');
		$humidity_alarms = $app->input->get('humidity_alarm', array(), 'array');

		$co2_ppm_alarm_thresholds = $app->input->get('co2_ppm_alarm_threshold', array(), 'array');
		$co2_ppm_alarmdirs = $app->input->get('co2_ppm_alarmdir', array(), 'array');
		$co2_ppm_alarms = $app->input->get('co2_ppm_alarm', array(), 'array');

		$co_sensor_alarm_thresholds = $app->input->get('co_sensor_alarm_threshold', array(), 'array');
		$co_sensor_alarmdirs = $app->input->get('co_sensor_alarmdir', array(), 'array');
		$co_sensor_alarms = $app->input->get('co_sensor_alarm', array(), 'array');

		$pm01_alarm_thresholds = $app->input->get('pm01_alarm_threshold', array(), 'array');
		$pm01_alarmdirs = $app->input->get('pm01_alarmdir', array(), 'array');
		$pm01_alarms = $app->input->get('pm01_alarm', array(), 'array');

		$pm25_alarm_thresholds = $app->input->get('pm25_alarm_threshold', array(), 'array');
		$pm25_alarmdirs = $app->input->get('pm25_alarmdir', array(), 'array');
		$pm25_alarms = $app->input->get('pm25_alarm', array(), 'array');
		
		$pm10_alarm_thresholds = $app->input->get('pm10_alarm_threshold', array(), 'array');
		$pm10_alarmdirs = $app->input->get('pm10_alarmdir', array(), 'array');
		$pm10_alarms = $app->input->get('pm10_alarm', array(), 'array');
		
		$tvoc_alarm_thresholds = $app->input->get('tvoc_alarm_threshold', array(), 'array');
		$tvoc_alarmdirs = $app->input->get('tvoc_alarmdir', array(), 'array');
		$tvoc_alarms = $app->input->get('tvoc_alarm', array(), 'array');
		
		$hcho_alarm_thresholds = $app->input->get('hcho_alarm_threshold', array(), 'array');
		$hcho_alarmdirs = $app->input->get('hcho_alarmdir', array(), 'array');
		$hcho_alarms = $app->input->get('hcho_alarm', array(), 'array');
		
		
    $obj = new stdClass;
		$obj->name = $name;
		$obj->ipaddr = $name;

		if($id > 0)
		{
        $items = TopHelpersTop::getGroupItem($id,TopHelpersTop::$dpage);
				if(count($items))
				{
					$obj->vendor = $items[0]->vendor;
					$obj->device_id = $items[0]->device_id;
					$obj->port = $items[0]->port;
					$obj->id = $items[0]->id;
					if($items[0]->vendor == TopHelpersTop::$dio_advantech_vendor)
					{
              $items = TopHelpersTop::getGroupItem($items[0]->parent,TopHelpersTop::$ddiopage);
							if(count($items))    $obj->ipaddr = $items[0]->name;
					}
					else if($items[0]->vendor == TopHelpersTop::$dio_icp_vendor)
					{
              $items = TopHelpersTop::getGroupItem($items[0]->parent,TopHelpersTop::$ddiopage);
							if(count($items))    $obj->ipaddr = $items[0]->name;
					}
				}
	  }

		$obj->addr = $addr;
		$obj->nc = $nc;
		$obj->dio_type = $dio_type;
		$obj->dio_type1 = $dio_type1;
		
		$obj->type = 1000;
    $obj->myid = $id;
    $obj->note1 = TopHelpersTop::$dpage;

    if($id == 0)
		{
			if(TopHelpersTop::issame_title($obj) == true)
			{
				JLog::add($obj->name." name is same", JLog::INFO, 'jerror');
				return;
			}

		    $this->add_new_item($obj);
        TopHelpersTop::init_new_device($obj);
       JLog::add(JText::_('COM_TOP_CHANGE_OK'), JLog::INFO, 'jerror');
			 return;
		}
		else
		{
        $this->change_old_item($obj);
		}

    $obj1 = new stdClass;

    $obj1->note1 = TopHelpersTop::$tdiopage;

		foreach ($id1 as $i=>$item)
		{
        $obj1->id = $item;
				$obj1->dio_id = $obj->myid;
				$obj1->index = $index[$i];

        // if($obj->vendor == TopHelpersTop::$dio_soyal_vendor)
				// {
				// 	$obj1->dio_type = 2;
				// }
				// else
				// {
					// }
					
				$obj1->alarm_threshold = $alarm_thresholds[$i];
				$obj1->dio_type = $dio_type[$i];
				$obj1->dio_value = 0;//$dio_value[$i];
				$obj1->show_node_text = $show_node_texts[$i];
				$obj1->trigger_alarm = $trigger_alarms[$i];
				$obj1->is_fault_node = $is_fault_nodes[$i];
				$obj1->display_last_alarm_time = $display_last_alarm_times[$i];
				$obj1->ba_app_enable = $ba_app_enables[$i];
				
				
				
				$obj1->co_sensor_alarm_threshold = $co_sensor_alarm_thresholds[$i];				
				$obj1->co_sensor_alarmdir = $co_sensor_alarmdirs[$i];
				$obj1->co_sensor_alarm = $co_sensor_alarms[$i];

				$obj1->temperature_alarm_threshold = $temperature_alarm_thresholds[$i];
				$obj1->temperature_alarmdir = $temperature_alarmdirs[$i];
				$obj1->temperature_alarm = $temperature_alarms[$i];

				$obj1->lux_alarm_threshold = $lux_alarm_thresholds[$i];
				$obj1->lux_alarmdir = $lux_alarmdirs[$i];
				$obj1->lux_alarm = $lux_alarms[$i];

				$obj1->humidity_alarm_threshold = $humidity_alarm_thresholds[$i];
				$obj1->humidity_alarmdir = $humidity_alarmdirs[$i];
				$obj1->humidity_alarm = $humidity_alarms[$i];

				$obj1->co2_ppm_alarm_threshold = $co2_ppm_alarm_thresholds[$i];
				$obj1->co2_ppm_alarmdir = $co2_ppm_alarmdirs[$i];
				$obj1->co2_ppm_alarm = $co2_ppm_alarms[$i];

				$obj1->pm01_alarm_threshold = $pm01_alarm_thresholds[$i];
				$obj1->pm01_alarmdir = $pm01_alarmdirs[$i];
				$obj1->pm01_alarm = $pm01_alarms[$i];

				$obj1->pm25_alarm_threshold = $pm25_alarm_thresholds[$i];
				$obj1->pm25_alarmdir = $pm25_alarmdirs[$i];
				$obj1->pm25_alarm = $pm25_alarms[$i];

				$obj1->pm10_alarm_threshold = $pm10_alarm_thresholds[$i];
				$obj1->pm10_alarmdir = $pm10_alarmdirs[$i];
				$obj1->pm10_alarm = $pm10_alarms[$i];

				$obj1->hcho_alarm_threshold = $hcho_alarm_thresholds[$i];
				$obj1->hcho_alarmdir = $hcho_alarmdirs[$i];
				$obj1->hcho_alarm = $hcho_alarms[$i];

				$obj1->tvoc_alarm_threshold = $tvoc_alarm_thresholds[$i];
				$obj1->tvoc_alarmdir = $tvoc_alarmdirs[$i];
				$obj1->tvoc_alarm = $tvoc_alarms[$i];

        $obj1->info = $info[$i];
		$obj1->note = $note[$i];
		$obj1->nc =$nc[$i];
        $obj1->group = $group[$i];
				$obj1->supergroup = $supergroup[$i];
        $obj1->enable = $device_enable[$i];

        $obj1->fontsize = $fontsize[$i];
				$obj1->color = $color[$i];

				$groups = TopHelpersTop::getGroupItem($obj1->group,$obj1->note1);

	      $obj1->width = 0;
				$obj1->height = 0;

				$obj1->green = '';
				$obj1->red = '';
				$obj1->yellow = '';

				if(count($groups) > 0)
				{
	          $obj1->width = $groups[0]->height;
						$obj1->height = $groups[0]->height;

						$obj1->green = $groups[0]->green;
						$obj1->red = $groups[0]->red;
						$obj1->yellow = $groups[0]->yellow;

				}

				$obj1->type = $top_floor[$i];

        $obj1->floor = $floors[$i];
				$obj1->dio_alarm = $app->input->getInt('dio_alarm'.$i, 0);
        //JLog::add('alarm '.$i.' '.$obj1->dio_alarm , JLog::INFO, 'jerror');

        $obj1->dio_alarmdir = $dio_alarmdir[$i];

				if($item == 0)
				    ;//TopHelpersTop::add_device($obj1);
				else
						TopHelpersTop::change_old_device($obj1);


		}

    if($obj->vendor < 101)
		    TopHelpersTop::save_device_file($obj);

		JLog::add(JText::_('COM_TOP_CHANGE_OK'), JLog::INFO, 'jerror');

  }


	protected function add_new_item($obj)
	{
	    $db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__top_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;


      // Create a new query object.
      $query = $db->getQuery(true);

      // Insert columns.
      $columns = array(
            'ordering',
            'state',
            'checked_out',
            'created_by',
            'name',
						'type',
						'note1',
						'ipaddr',
            );

      // Insert values.
      $values = array(
            $db->quote($ordering),
            $db->quote($state),
            $db->quote($checked_out),
            $db->quote($created_by),
						$db->quote($obj->name),
						$db->quote($obj->type),
						$db->quote($obj->note1),
						$db->quote($obj->name),

            );

      // Prepare the insert query.
      $query
            ->insert($db->quoteName('#__top_table'))
            ->columns($db->quoteName($columns))
            ->values(implode(',', $values));

            // Set the query using our newly populated query object and execute it.
      $db->setQuery($query);
      $db->execute();

			$db->setQuery('SELECT MAX(id) FROM #__top_table');
	    $max = $db->loadResult();

      $obj->myid = $max;
		  $this->setState('list_myid', $max);


	}
	protected function change_old_item($obj)
	{
      $db = JFactory::getDbo();

			$fields = array(
				$db->quoteName('name') . ' = ' . $db->quote($obj->name),
				$db->quoteName('type') . ' = ' . $db->quote($obj->type),
				$db->quoteName('addr') . ' = ' . $db->quote($obj->addr)				
			);
			
			if($obj->dio_type == TopHelpersTop::$dio_co_sensor_dio_type || $obj->dio_type == TopHelpersTop::$dio_co_sensor_general_dio_type || $obj->dio_type == TopHelpersTop::$dio_co_sensor_yon_gjia_dio_type)
			{
				$fields1 = array(
					$db->quoteName('co_sensor_alarm_threshold') . ' = ' . $db->quote($obj->co_sensor_alarm_threshold),
				);

				$fields = array_merge($fields,$fields1);
			}
      if($obj->vendor == TopHelpersTop::$dio_weema_vendor)
			{
			    $fields1 = array(
				      $db->quoteName('ipaddr') . ' = ' . $db->quote($obj->ipaddr),
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
			    );

					$fields = array_merge($fields,$fields1);

					if ($obj->dio_type == TopHelpersTop::$dio_temp_humidity_dio_type || $obj->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity_dio_type || $obj->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity2_dio_type)
					{
						$fields1 = array(
							$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
							$db->quoteName('temperature_alarm_alarmdir') . ' = ' . $db->quote($obj->temperature_alarm_alarmdir),
							$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),
							$db->quoteName('humidity_alarm_threshold') . ' = ' . $db->quote($obj->humidity_alarm_threshold),
							$db->quoteName('humidity_alarm_alarmdir') . ' = ' . $db->quote($obj->humidity_alarm_alarmdir),
							$db->quoteName('humidity_alarm') . ' = ' . $db->quote($obj->humidity_alarm),
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
						);

						$fields = array_merge($fields,$fields1);						
					}
					else if ($obj->dio_type == TopHelpersTop::$dio_co2_dio_type)
					{
						$fields1 = array(
							$db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold)
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
						);
						$fields = array_merge($fields,$fields1);	
					}
					else if ($obj->dio_type == TopHelpersTop::$dio_weema_iaq_dio_type || $obj->dio_type == TopHelpersTop::$dio_pm_sensor_yon_gjia_dio_type)
					{
						$fields1 = array(
							$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
							$db->quoteName('temperature_alarm_alarmdir') . ' = ' . $db->quote($obj->temperature_alarm_alarmdir),
							$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),
							$db->quoteName('humidity_alarm_threshold') . ' = ' . $db->quote($obj->humidity_alarm_threshold),
							$db->quoteName('humidity_alarm_alarmdir') . ' = ' . $db->quote($obj->humidity_alarm_alarmdir),
							$db->quoteName('humidity_alarm') . ' = ' . $db->quote($obj->humidity_alarm),
							$db->quoteName('co_sensor_alarm_threshold') . ' = ' . $db->quote($obj->co_sensor_alarm_threshold),
							$db->quoteName('co_sensor_alarm_alarmdir') . ' = ' . $db->quote($obj->co_sensor_alarm_alarmdir),
							$db->quoteName('co_sensor_alarm') . ' = ' . $db->quote($obj->co_sensor_alarm),
							$db->quoteName('co2_ppm_alarm_threshold') . ' = ' . $db->quote($obj->co2_ppm_alarm_threshold),
							$db->quoteName('co2_ppm_alarm_alarmdir') . ' = ' . $db->quote($obj->co2_ppm_alarm_alarmdir),
							$db->quoteName('co2_ppm_alarm') . ' = ' . $db->quote($obj->co2_ppm_alarm),
							$db->quoteName('tvoc_alarm_threshold') . ' = ' . $db->quote($obj->tvoc_alarm_threshold),
							$db->quoteName('tvoc_alarm_alarmdir') . ' = ' . $db->quote($obj->tvoc_alarm_alarmdir),
							$db->quoteName('tvoc_alarm') . ' = ' . $db->quote($obj->tvoc_alarm),
							$db->quoteName('hcho_alarm_threshold') . ' = ' . $db->quote($obj->hcho_alarm_threshold),
							$db->quoteName('hcho_alarm_alarmdir') . ' = ' . $db->quote($obj->hcho_alarm_alarmdir),
							$db->quoteName('hcho_alarm') . ' = ' . $db->quote($obj->hcho_alarm),
							$db->quoteName('pm01_alarm_threshold') . ' = ' . $db->quote($obj->pm01_alarm_threshold),
							$db->quoteName('pm01_alarm_alarmdir') . ' = ' . $db->quote($obj->pm01_alarm_alarmdir),
							$db->quoteName('pm01_alarm') . ' = ' . $db->quote($obj->pm01_alarm),
							$db->quoteName('pm25_alarm_threshold') . ' = ' . $db->quote($obj->pm25_alarm_threshold),
							$db->quoteName('pm25_alarm_alarmdir') . ' = ' . $db->quote($obj->pm25_alarm_alarmdir),
							$db->quoteName('pm25_alarm') . ' = ' . $db->quote($obj->pm25_alarm),
							$db->quoteName('pm10_alarm_threshold') . ' = ' . $db->quote($obj->pm10_alarm_threshold),
							$db->quoteName('pm10_alarm_alarmdir') . ' = ' . $db->quote($obj->pm10_alarm_alarmdir),
							$db->quoteName('pm10_alarm') . ' = ' . $db->quote($obj->pm10_alarm),
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
						);

						$fields = array_merge($fields,$fields1);	
					}
					else if ($obj->dio_type == TopHelpersTop::$dio_yon_gjia_temp_lux_dio_type)
					{
						$fields1 = array(
							$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
							$db->quoteName('temperature_alarm_alarmdir') . ' = ' . $db->quote($obj->temperature_alarm_alarmdir),
							$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),
							$db->quoteName('lux_alarm_threshold') . ' = ' . $db->quote($obj->lux_alarm_threshold),
							$db->quoteName('lux_alarm_alarmdir') . ' = ' . $db->quote($obj->lux_alarm_alarmdir),
							$db->quoteName('lux_alarm') . ' = ' . $db->quote($obj->lux_alarm),
						);
						$fields = array_merge($fields,$fields1);
					}
					else if ($obj->dio_type == TopHelpersTop::$dio_jnc_temp_humidity_dio_type || $obj->dio_type == TopHelpersTop::$dio_temp_humidity_pinron_dio_type || $obj->dio_type == TopHelpersTop::$dio_yon_gjia_temp_humidity_3_in_1_dio_type ||$obj->dio_type == TopHelpersTop::$dio_temp_tcs5282)
					{
						$fields1 = array(
							$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
							$db->quoteName('temperature_alarm_alarmdir') . ' = ' . $db->quote($obj->temperature_alarm_alarmdir),
							$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),
							$db->quoteName('humidity_alarm_threshold') . ' = ' . $db->quote($obj->humidity_alarm_threshold),
							$db->quoteName('humidity_alarm_alarmdir') . ' = ' . $db->quote($obj->humidity_alarm_alarmdir),
							$db->quoteName('humidity_alarm') . ' = ' . $db->quote($obj->humidity_alarm),
							$db->quoteName('co2_ppm_alarm_threshold') . ' = ' . $db->quote($obj->co2_ppm_alarm_threshold),
							$db->quoteName('co2_ppm_alarm_alarmdir') . ' = ' . $db->quote($obj->co2_ppm_alarm_alarmdir),
							$db->quoteName('co2_ppm_alarm') . ' = ' . $db->quote($obj->co2_ppm_alarm),
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
						);

						$fields = array_merge($fields,$fields1);	
					}
					else if ($obj->dio_type == TopHelpersTop::$dio_temp_tcs30a22)
					{
						$fields1 = array(
							$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
							$db->quoteName('temperature_alarm_alarmdir') . ' = ' . $db->quote($obj->temperature_alarm_alarmdir),
							$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),							
							$db->quoteName('dio_alarmdir') . ' = ' . $db->quote($obj->dio_alarmdir),
							$db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
							$db->quoteName('dio_alarm') . ' = ' . $db->quote($obj->dio_alarm),
							// $db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
						);

						$fields = array_merge($fields,$fields1);	
					}
		  }
	if($obj->vendor == TopHelpersTop::$dio_soyal_vendor)
	{
		$fields1 = array(
			$db->quoteName('device_id') . ' = ' . $db->quote($obj->addr),

		);

		$fields = array_merge($fields,$fields1);
	}
	  if (
		$obj->vendor == TopHelpersTop::$dio_yunyangFireFighter_vendor ||
	  	$obj->vendor == TopHelpersTop::$dio_baochungFireFighter_vendor)
	  {
		$fields1 = array(
			$db->quoteName('device_id') . ' = ' . $db->quote($obj->addr),

	  	);

		$fields = array_merge($fields,$fields1);
	  }
	  if($obj->vendor == TopHelpersTop::$dio_liuchuanElevator_vendor ||
	  $obj->vendor == TopHelpersTop::$dio_mitsubishiElevator_vendor ||
	  $obj->vendor == TopHelpersTop::$dio_fujiElevator_vendor)
	  {
		$fields1 = array(
			$db->quoteName('device_id') . ' = ' . $db->quote($obj->addr),

	  	);

		$fields = array_merge($fields,$fields1);
	  } else 
	  {
		$dio_type_field = array(
			$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type1)
		  );
	  }
			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->myid)
			);
      $query = $db->getQuery(true);
      $query->update($db->quoteName('#__top_table'))->set($fields)->where($conditions);

	    $db->setQuery($query)->execute();
       //JLog::add($query, JLog::INFO, 'jerror');
	}
	/**
	 * Method to auto-populate the model state.
	 *
	 * Note. Calling getState in this method will result in recursion.
	 *
	 * @param   string  $ordering   Elements order
	 * @param   string  $direction  Order direction
	 *
	 * @return void
	 *
	 * @throws Exception
	 *
	 * @since    1.6
	 */
	protected function populateState($ordering = null, $direction = null)
	{
		$this->wa_populateState($ordering,$direction);
            $app  = JFactory::getApplication();
		$list = $app->getUserState($this->context . '.list');

		$ordering  = isset($list['filter_order'])     ? $list['filter_order']     : null;
		$direction = isset($list['filter_order_Dir']) ? $list['filter_order_Dir'] : null;

		$list['limit']     = $app->getUserStateFromRequest($this->context . '.list.limit', 'limit', $app->get('list_limit'), 'uint');
		$list['start']     = $app->input->getInt('start', 0);
		$list['ordering']  = $ordering;
		$list['direction'] = $direction;

		$app->setUserState($this->context . '.list', $list);
		$app->input->set('list', null);


        // List state information.


		parent::populateState('a.ordering', 'asc');

        $context = $this->getUserStateFromRequest($this->context.'.filter.search', 'filter_search');
        $this->setState('filter.search', $context);

        // Split context into component and optional section
        $parts = FieldsHelper::extract($context);

        if ($parts)
        {
            $this->setState('filter.component', $parts[0]);
            $this->setState('filter.section', $parts[1]);
        }
	}

	/**
	 * Build an SQL query to load the list data.
	 *
	 * @return   JDatabaseQuery
	 *
	 * @since    1.6
	 */
	protected function getListQuery()
	{
            // Create a new query object.
            $db    = $this->getDbo();
            $query = $db->getQuery(true);

            // Select the required fields from the table.
            $query->select(
                        $this->getState(
                                'list.select', 'DISTINCT a.*'
                        )
                );

            $query->from('`#__top_table` AS a');

		// Join over the users for the checked out user.
		$query->select('uc.name AS uEditor');
		$query->join('LEFT', '#__users AS uc ON uc.id=a.checked_out');

		// Join over the created by field 'created_by'
		$query->join('LEFT', '#__users AS created_by ON created_by.id = a.created_by');

		// Join over the created by field 'modified_by'
		$query->join('LEFT', '#__users AS modified_by ON modified_by.id = a.modified_by');

		if (!Factory::getUser()->authorise('core.edit', 'com_top'))
		{
			$query->where('a.state = 1');
		}

		$query->where('a.state = 1');
		$query->where('a.note1 = '.TopHelpersTop::$dpage);

		$id = $this->getState('list_myid');

		$query->where('a.id = ' . $id);

            // Filter by search in title
            $search = $this->getState('filter.search');

            if (!empty($search))
            {
                if (stripos($search, 'id:') === 0)
                {
                    $query->where('a.id = ' . (int) substr($search, 3));
                }
                else
                {
                    $search = $db->Quote('%' . $db->escape($search, true) . '%');
                }
            }


            // Add the list ordering clause.
            $orderCol  = $this->state->get('list.ordering', 'checked_out');
            $orderDirn = $this->state->get('list.direction', 'ASC');

            if ($orderCol && $orderDirn)
            {
                $query->order($db->escape($orderCol . ' ' . $orderDirn));
            }

            return $query;
	}

	/**
	 * Method to get an array of data items
	 *
	 * @return  mixed An array of data on success, false on failure.
	 */
	public function getItems()
	{
		$items = parent::getItems();


		return $items;
	}

	/**
	 * Overrides the default function to check Date fields format, identified by
	 * "_dateformat" suffix, and erases the field if it's not correct.
	 *
	 * @return void
	 */
	protected function loadFormData()
	{
		$app              = Factory::getApplication();
		$filters          = $app->getUserState($this->context . '.filter', array());
		$error_dateformat = false;

		foreach ($filters as $key => $value)
		{
			if (strpos($key, '_dateformat') && !empty($value) && $this->isValidDate($value) == null)
			{
				$filters[$key]    = '';
				$error_dateformat = true;
			}
		}

		if ($error_dateformat)
		{
			$app->enqueueMessage(Text::_("COM_TOP_SEARCH_FILTER_DATE_FORMAT"), "warning");
			$app->setUserState($this->context . '.filter', $filters);
		}

		return parent::loadFormData();
	}

	/**
	 * Checks if a given date is valid and in a specified format (YYYY-MM-DD)
	 *
	 * @param   string  $date  Date to be checked
	 *
	 * @return bool
	 */
	private function isValidDate($date)
	{
		$date = str_replace('/', '-', $date);
		return (date_create($date)) ? Factory::getDate($date)->format("Y-m-d") : null;
	}
}
