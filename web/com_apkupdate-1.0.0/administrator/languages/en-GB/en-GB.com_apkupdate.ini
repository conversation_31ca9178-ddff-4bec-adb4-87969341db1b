COM_APKUPDATE="Apkupdate"
COM_APKUPDATE_COMPONENT_LABEL="Apkupdate"
COM_APKUPDATE_CONFIGURATION="Apkupdate Configuration"
COM_APKUPDATE_ACCESS_HEADING="Access"
COM_APKUPDATE_COMPONENT_DESC=""
COM_APKUPDATE_N_ITEMS_ARCHIVED="%d items successfully archived"
COM_APKUPDATE_N_ITEMS_ARCHIVED_1="%d item successfully archived"
COM_APKUPDATE_N_ITEMS_CHECKED_IN_0="No item successfully checked in"
COM_APKUPDATE_N_ITEMS_CHECKED_IN_1="%d item successfully checked in"
COM_APKUPDATE_N_ITEMS_CHECKED_IN_MORE="%d items successfully checked in"
COM_APKUPDATE_N_ITEMS_DELETED="%d items successfully deleted"
COM_APKUPDATE_N_ITEMS_DELETED_1="%d item successfully deleted"
COM_APKUPDATE_N_ITEMS_PUBLISHED="%d items successfully published"
COM_APKUPDATE_N_ITEMS_PUBLISHED_1="%d item successfully published"
COM_APKUPDATE_N_ITEMS_TRASHED="%d items successfully trashed"
COM_APKUPDATE_N_ITEMS_TRASHED_1="%d item successfully trashed"
COM_APKUPDATE_N_ITEMS_UNPUBLISHED="%d items successfully unpublished"
COM_APKUPDATE_N_ITEMS_UNPUBLISHED_1="%d item successfully unpublished"
COM_APKUPDATE_NO_ITEM_SELECTED="No items selected"
COM_APKUPDATE_SAVE_SUCCESS="Item successfully saved"
COM_APKUPDATE_ITEM_ID_SELECT_LABEL="Select the item ID"
COM_APKUPDATE_ITEM_ID_SELECT_LABEL_FORM="Select the Item ID to Edit (Set up as 0 if you want to set up as add form)"
COM_APKUPDATE_FIELDSET_ITEM_ID_SELECT_LABEL="Required Settings"
COM_APKUPDATE_FILTER_SELECT_LABEL=" - Select %s - "
COM_APKUPDATE_TEST_LABEL="Test label"
COM_APKUPDATE_FIELDSET_RULES="Permissions"
COM_APKUPDATE_FROM_FILTER="From %s"
COM_APKUPDATE_TO_FILTER="To %s"
COM_APKUPDATE_VIEW_FILE="[View File]"
COM_APKUPDATE_ITEMS_SUCCESS_DUPLICATED="Items successfully duplicated"

COM_APKUPDATE_SEARCH_FILTER_SUBMIT = "Search"
COM_APKUPDATE_SEARCH_TOOLS = "Search Tools"
COM_APKUPDATE_SEARCH_TOOLS_DESC = "Filter the list items"
COM_APKUPDATE_SEARCH_FILTER_CLEAR = "Clear filter"

COM_APKUPDATE_TITLE_TABLES = "Tables"
COM_APKUPDATE_TABLES_ID = "ID"
COM_APKUPDATE_TABLES_ORDERING = "Order"
COM_APKUPDATE_TABLES_STATE = "State"
COM_APKUPDATE_TABLES_CHECKED_OUT = "N/A"
COM_APKUPDATE_TABLES_CHECKED_OUT_TIME = "N/A"
COM_APKUPDATE_TABLES_CREATED_BY = "Created by"
COM_APKUPDATE_TABLES_MODIFIED_BY = "Modified by"
COM_APKUPDATE_TABLES_APP = "App"
COM_APKUPDATE_TABLES_VERSION = "Version"
COM_APKUPDATE_TABLES_PATH = "Path"
COM_APKUPDATE_TABLES_ENABLE = "Enable"
COM_APKUPDATE_TABLES_NAME = "Name"
COM_APKUPDATE_TABLES_NOTE = "AppId"
COM_APKUPDATE_TABLES_FILE = "File"
COM_APKUPDATE_TABLES_PARENT = "Parent"
COM_APKUPDATE_TABLES_NOTE1 = "Channel"
COM_APKUPDATE_ID_DESC = "ID Descending"
COM_APKUPDATE_ORDERING_DESC = "Order Descending"
COM_APKUPDATE_STATE_DESC = "State Descending"
COM_APKUPDATE_CHECKED_OUT_DESC = "N/A Descending"
COM_APKUPDATE_CHECKED_OUT_TIME_DESC = "N/A Descending"
COM_APKUPDATE_CREATED_BY_DESC = "Created by Descending"
COM_APKUPDATE_MODIFIED_BY_DESC = "Modified by Descending"
COM_APKUPDATE_APP_DESC = "App Descending"
COM_APKUPDATE_VERSION_DESC = "Version Descending"
COM_APKUPDATE_PATH_DESC = "Path Descending"
COM_APKUPDATE_ENABLE_DESC = "Enable Descending"
COM_APKUPDATE_NAME_DESC = "Name Descending"
COM_APKUPDATE_NOTE_DESC = "Note Descending"
COM_APKUPDATE_FILE_DESC = "File Descending"
COM_APKUPDATE_PARENT_DESC = "Parent Descending"
COM_APKUPDATE_NOTE1_DESC = "Note1 Descending"
COM_APKUPDATE_ID_ASC = "ID Ascending"
COM_APKUPDATE_ORDERING_ASC = "Order Ascending"
COM_APKUPDATE_STATE_ASC = "State Ascending"
COM_APKUPDATE_CHECKED_OUT_ASC = "N/A Ascending"
COM_APKUPDATE_CHECKED_OUT_TIME_ASC = "N/A Ascending"
COM_APKUPDATE_CREATED_BY_ASC = "Created by Ascending"
COM_APKUPDATE_MODIFIED_BY_ASC = "Modified by Ascending"
COM_APKUPDATE_APP_ASC = "App Ascending"
COM_APKUPDATE_VERSION_ASC = "Version Ascending"
COM_APKUPDATE_PATH_ASC = "Path Ascending"
COM_APKUPDATE_ENABLE_ASC = "Enable Ascending"
COM_APKUPDATE_NAME_ASC = "Name Ascending"
COM_APKUPDATE_NOTE_ASC = "Note Ascending"
COM_APKUPDATE_FILE_ASC = "File Ascending"
COM_APKUPDATE_PARENT_ASC = "Parent Ascending"
COM_APKUPDATE_NOTE1_ASC = "Note1 Ascending"

COM_APKUPDATE_TITLE_TABLE = "Table"
COM_APKUPDATE_LEGEND_TABLE = "Table"
COM_APKUPDATE_FORM_LBL_TABLE_ID = "ID"
COM_APKUPDATE_FORM_DESC_TABLE_ID = ""
COM_APKUPDATE_FORM_LBL_TABLE_ORDERING = "Order"
COM_APKUPDATE_FORM_DESC_TABLE_ORDERING = ""
COM_APKUPDATE_FORM_LBL_TABLE_STATE = "State"
COM_APKUPDATE_FORM_DESC_TABLE_STATE = ""
COM_APKUPDATE_FORM_LBL_TABLE_CHECKED_OUT = "N/A"
COM_APKUPDATE_FORM_DESC_TABLE_CHECKED_OUT = ""
COM_APKUPDATE_FORM_LBL_TABLE_CHECKED_OUT_TIME = "N/A"
COM_APKUPDATE_FORM_DESC_TABLE_CHECKED_OUT_TIME = ""
COM_APKUPDATE_FORM_LBL_TABLE_CREATED_BY = "Created by"
COM_APKUPDATE_FORM_DESC_TABLE_CREATED_BY = ""
COM_APKUPDATE_FORM_LBL_TABLE_MODIFIED_BY = "Modified by"
COM_APKUPDATE_FORM_DESC_TABLE_MODIFIED_BY = ""
COM_APKUPDATE_FORM_LBL_TABLE_APP = "App"
COM_APKUPDATE_FORM_DESC_TABLE_APP = ""
COM_APKUPDATE_TAB_TABLE = "Table"
COM_APKUPDATE_FIELDSET_TABLE = "Table"
COM_APKUPDATE_FORM_LBL_TABLE_VERSION = "Version"
COM_APKUPDATE_FORM_DESC_TABLE_VERSION = ""
COM_APKUPDATE_FORM_LBL_TABLE_PATH = "Path"
COM_APKUPDATE_FORM_DESC_TABLE_PATH = ""
COM_APKUPDATE_FORM_LBL_TABLE_ENABLE = "Enable"
COM_APKUPDATE_FORM_DESC_TABLE_ENABLE = ""
COM_APKUPDATE_FORM_LBL_TABLE_NAME = "Name"
COM_APKUPDATE_FORM_DESC_TABLE_NAME = ""
COM_APKUPDATE_FORM_LBL_TABLE_NOTE = "AppId"
COM_APKUPDATE_FORM_DESC_TABLE_NOTE = ""
COM_APKUPDATE_FORM_LBL_TABLE_FILE = "File"
COM_APKUPDATE_FORM_DESC_TABLE_FILE = ""
COM_APKUPDATE_FORM_LBL_TABLE_PARENT = "Parent"
COM_APKUPDATE_FORM_DESC_TABLE_PARENT = ""
COM_APKUPDATE_FORM_LBL_TABLE_NOTE1 = "Channel"
COM_APKUPDATE_FORM_DESC_TABLE_NOTE1 = ""
COM_APKUPDATE_TITLE_LIST_VIEW_TABLES = "Tables"
COM_APKUPDATE_TITLE_LIST_VIEW_TABLES_DESC = "Show a list of Tables"
COM_APKUPDATE_TITLE_ITEM_VIEW_TABLE = "Single Table"
COM_APKUPDATE_TITLE_ITEM_VIEW_TABLE_DESC = "Show a specific Table"
COM_APKUPDATE_TITLE_FORM_VIEW_TABLE = "TableForm"
COM_APKUPDATE_TITLE_FORM_VIEW_TABLE_DESC = "Show a form to add or edit a Table"




