<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Floor
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2018 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;

HTMLHelper::addIncludePath(JPATH_COMPONENT . '/helpers/html');
HTMLHelper::_('bootstrap.tooltip');
HTMLHelper::_('behavior.multiselect');
HTMLHelper::_('formbehavior.chosen', 'select');

$user       = Factory::getUser();
$userId     = $user->get('id');
$userName     = $user->get('name');
$name     = $user->get('username');
$listOrder  = $this->state->get('list.ordering');
$listDirn   = $this->state->get('list.direction');
$canCreate  = $user->authorise('core.create', 'com_floor');// && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canEdit    = $user->authorise('core.edit', 'com_floor') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canCheckin = $user->authorise('core.manage', 'com_floor');
$canChange  = $user->authorise('core.edit.state', 'com_floor');
$canDelete  = $user->authorise('core.delete', 'com_floor');

$canCreate  = true;
$doc = Factory::getDocument();
$doc->addScript(Uri::base() . '/media/com_floor/js/humanize-duration.min.js');
$doc->addScript(Uri::base() . '/media/com_floor/js/toastr.min.js');
$doc->addStyleSheet(Uri::base() . '/media/com_floor/css/toastr.min.css');
// $doc->addScript(Uri::base() . '/media/com_floor/js/jquery.toast.min.js');
//make sure user is logged in
if($user->id == 0)
{
	JError::raiseWarning( 403, JText::_( 'COM_FLOOR_ERROR_MUST_LOGIN') );
	$joomlaLoginUrl = 'index.php?option=com_users&view=login';

	echo "<br><a href='".JRoute::_($joomlaLoginUrl)."'>".JText::_( 'COM_FLOOR_LOG_IN')."</a><br>";
        return;
}

$mygroup  = $this->state->get('list_mygroup');

$myFloor = '';
$myid  = $this->state->get('list_myid');

$myfilter_building  = $this->state->get('list_myfilter_building');

$is_enable = 1;
$devices = FloorHelpersFloor::get_all_devices($myid,$mygroup,$is_enable);

$suppress_auto_redirect = FloorHelpersFloor::getIfUserSuppressedAutoRedirect($user);
$number = count($devices);

if($number == 0)
{
  JLog::add("無裝置", JLog::INFO, 'jerror');
  //return;
}
else
{
	//JLog::add("裝置 ".$number, JLog::INFO, 'jerror');
}

$is_spacial_floor = false;


$building_items = FloorHelpersFloor::getBuildingItem($this->items[0]->building);

if(count($building_items))
{
    if($building_items[0]->type == FloorHelpersFloor::$building_elec)
		{
			$is_spacial_floor = true;
		}
}
/*
if($is_spacial_floor == true)
{
	$elec_devs = FloorHelpersFloor::get_elec_devices($myid,$mygroup,$is_enable);

	//JLog::add("裝置 ".count($elec_devs), JLog::INFO, 'jerror');

}
*/
foreach($devices as $i=>$item)
{

  if($item->fontsize == 0)
  {
    $item->fontsize = 3;
    $item->color = 'black';
  }


}


$infos = TopHelpersUtility::get_info_msg();

// $devicelog = FloorHelpersFloor::get_devicelog();
$devicelog = FloorHelpersFloor:: get_latest_device_logs(5);

$offline = FloorHelpersFloor::getOfflineDevices();

$mycctv = FloorHelpersFloor::getOpenDevices();

$mycctv1 = FloorHelpersFloor::getOpenDevicesphone();

$mycctvtop = TopHelpersUtility::getCCTVTop();

		list($width, $height, $type, $attr) = getimagesize($this->items[0]->path);
		//JLog::add(count($this->items[0]), JLog::INFO, 'jerror');
		//JLog::add($this->items[0]->path, JLog::INFO, 'jerror');

    if($width == "")
		{
        $width = 1440;
		    $height = 900;
	  }

		$myaccounts = TopHelpersUtility::getMyAccount();
    $accs = $myaccounts;
		$phone_min = 200;
		$phone_max = 3000;
    $def_number = 130;

		if(count($myaccounts))
		{
			$phone_min = $myaccounts[0]->note1;
			$phone_max = $myaccounts[0]->note2;
			$def_number = $myaccounts[0]->note3;

		}




if(true || $is_spacial_floor)
{

    $alarm_kw = $accs[0]->elec_alarm;
		$cur_kw = $accs[0]->elec_kw;
}
?>

<style>


@keyframes longblink {
  80% {
    opacity: 0;
  }
}
/* @keyframes longblink{
  0%, 20% {
    opacity: 0;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
} */
/* @keyframes movex{
  0%{transform : translate(-100px, 0)}
  100%{transform : translate(100px, 0)}
}

@keyframes movey{
  0%{transform : translate(0, 0);}
  25%{transform : translate(0, -100px);}
  50%{transform : translate(0, 0); background: aqua;}
  75%{transform : translate(0, 100px);} 
  100%{transform : translate(0, 0px);}
}

.licenseMoveX{
  position: absolute;
  animation: movex 1s ease-in-out alternate infinite;
}
.licenseMoveY{
  background : #fff;
  width: 50px; height: 50px;
  border-radius : 50%;
  animation : movey 1s linear infinite;
} */
.licenseNotification {
	position:absolute;
	/* top:42%; */
	letter-spacing: 20px;
	left:25%;
	color:red;
	font-size:125px;
	-webkit-text-stroke: 2px yellow;	
	z-index: 99999;
	pointer-events: none;

}
/* .licenseNotification:nth-of-type(2)
{
	animation-delay:.3s;	
}
.licenseNotification:nth-of-type(3)
{
	animation-delay:.6s;	
}
.licenseNotification:nth-of-type(4)
{
	animation-delay:.9s;	
}
.licenseNotification:nth-of-type(5)
{
	animation-delay:1.2s;	
}
.licenseNotification:nth-of-type(6)
{
	animation-delay:1.5s;	
}
.licenseNotification:nth-of-type(7)
{
	animation-delay:1.8s;	
}
.licenseNotification:nth-of-type(8)
{
	animation-delay:2.1s;	
}
.licenseNotification:nth-of-type(9)
{
	animation-delay:2.4s;	
}
.licenseNotification:nth-of-type(10)
{
	animation-delay:2.7s;	
} */
#licenseOverlay
{
	position:absolute;
	width:110%;
	height:145%;
	color:grey;
	z-index: 99999;
	background-color: #080d15;
	opacity: .3;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: none;
	left:0px;
	top:0px
}
.alarm_node {
	background-color: #ff5454;
	border-radius: 20px;
}

.alarm_menu {
	background-color: #ff5454;
	border-radius: 20px;
}
.blinking_node {
  animation: blinker 1s linear infinite;
  back
}

@keyframes blinker {  
  90% { opacity: 0; }
}
.blink-bg{
		/* color: #fff; */
		/* padding: 10px; */
		/* display: inline-block; */
		/* border-radius: 5px; */
		border-radius: 20px;
		animation: blinkingBackground 1s infinite;
}
@keyframes blinkingBackground{
	0%		{ background-color: #ffffff;}
	/* 25%		{ background-color: #1056c0;} */
	/* 50%		{ background-color: #ef0a1a;} */
	/* 75%		{ background-color: #254878;} */
	50%	        { background-color: #ff5454;}
}

.disablepagediv {
    z-index: 1001;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: none;
    position: absolute;
    background-color: rgba(0,0,0,0.5);
    color: #aaaaaa;
}


#navigator ul .parent_current span {
    display: block;
    width: 100%;
    height: 31px;
    line-height: 40px;
    color: #000000;
    font-size: 20px;
    font-weight: bold;
    text-indent: 10px;
    background-color: #ccf2ff;
    //border-radius: 6px 2px 6px 2px;
}

#navigator ul .son_current span {
    display: block;
    width: 100%;
    height: 31px;
    line-height: 40px;
    color: #DDDDDD;
    font-weight: bold;
    text-indent: 24px;
    background-color: #b3c6ff;
    //border-radius: 6px 2px 6px 2px;
}
#navigator ul {
    list-style-type: none;
    list-style-image: none;
}
#navigator {
    font: 14px/31px "Microsoft YaHei", Arial, Helvetica;
        font-weight: normal;
        font-size: 20px;
        line-height: 40px;
}
#navigator .building_son{

		box-shadow: 0px 4px 6px 0px #dedede;
		background:linear-gradient(to bottom, #bdbbbd 5%, #a19fa1 100%);
		background-color:#bdbbbd;
		border-radius:4px;
		//display: block;
		display:inline-block;
		cursor:pointer;
		color:#000000;
		font-family:Courier New;
		font-size:20px;
		font-weight:bold;
		padding:5px 30px;
		text-decoration:none;
		margin-top:20px
		margin-left:20px
		text-align: center;
}

#navigator .building_son:hover {
	background: #4d79ff;
  //background-color: #008CBA;
  color: white;
}


#navigator ul .son a {
    display: block;
    width: 100%;
    line-height: 40px;
    color: #000000;
    text-indent: 24px;
    text-decoration: none;
    border-radius: 6px 2px 6px 2px;
		//display: none;
}
#navigator ul .parent a {
    display: block;
    width: 100%;
    height: 31px;
    line-height: 40px;
    color: #000000;
    font-size: 20px;
    font-weight: bold;
    text-indent: 10px;
    text-decoration: none;
    border-radius: 6px 2px 6px 2px;
}
ul, ol {
    padding: 0;
    margin: 0;
}

#container_right {
    //float: right;
    //width: 1200px;
    border: 1px solid green;
    overflow: hidden; /* if you don't want #second to wrap below #first */
}
#container_left {

    float: left;
    width: 198px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}

#wrapper {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow: hidden; /* will contain if #first is longer than #second */
    //background-color: #F7F7F7;
}

table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}
th, td {
  padding: 5px;
  //text-align: left;
}

#mydiv {
	background-image: url("/<?php echo $this->items[0]->path;?>");
	background-size: <?php echo $width;?>px <?php echo $height;?>px;

}

#mydiv canvas {
	margin: 0;
	padding: 0;
	width: <?php echo $width;?>px;
	height:<?php echo $height;?>px;
}

.mywidth
{
	width: 300px;

	  margin: 20px;
	  padding: 10px;
  float:left;
}

.mymenu
{
	font-size: 18px;

}

body{
	width:1920px;
}
ul {
  /* 取消ul預設的內縮及樣式 */
  margin: 0;
  padding: 0;
  list-style: none;
}

th,td,h3{
	text-align: center;
}
</style>


<div id="errorDiv">
</div>

<form action="<?php echo htmlspecialchars(Uri::getInstance()->toString()); ?>" method="post"
      name="adminForm" id="adminForm">

      <div id="wrapper">
        <div id="container_left">

          <div id="navigator">

            <ul>
                <?php
                $building_items=FloorHelpersFloor::getBuildingItem($myfilter_building);
                ?>

                <?php foreach($building_items as $i2 => $item2):?>

                  <?php
									$son_items=FloorHelpersFloor::get_all_building_floor($item2->id);

                  $my_building = 'building_son';
                  $building_style = 'style="display:none"';
                  ?>

									<?php
									    foreach($son_items as $i1 => $item1)
                      {
													 if($item1->id == $myid)
													 {
														 $building_style = 'style="display:block"';
														 break;
													 }
											}
										?>

                  <li class="<?php echo $my_building;?>" onclick="liclick('<?php echo($item2->id);?>')"><?php echo($item2->name);?><span class="building_alarm_count" id="building_alarm_count_<?php echo($item2->id); ?>"></span></li>

									<span id="<?php echo($item2->id);?>" <?php echo($building_style);?>>
                  <?php foreach($son_items as $i1 => $item1):?>
                    <?php
                    $my_son = 'son';

                    if($item1->id == $myid)
                    {
                      $my_son = 'son_current';

											$myFloor = $item1->name;
                    }
										//JLog::add("item1 ".$item1->id." ".$item1->name, JLog::WARNING, 'jerror');
                    ?>

                    <li class="<?php echo $my_son;?>" ><span><a href="<?php echo JRoute::_('index.php?option=com_floor&task=sroots&id=' . $item1->id.'&group='.$mygroup.'&building='.$item2->id.'&filter_building='.$myfilter_building, false, 2)?>" onfocus="javascript:this.blur();"><?php echo $item1->name;?><text class="floor_alarm_count" id="floor_alarm_count_<?php echo($item1->id); ?>"></text></a></span></li>

                  <?php endforeach;?>
								</span>
                <?php endforeach;?>

            </ul>
          </div>
        </div>

      	<div id="container_right">
		  <?php if($is_spacial_floor):?>
         	<span>
         		<button id="save1" class="btn" type="button"><img src="/media/com_top/img/儲存-01.png" width="68" height="26" ></button>

          	</span>
			<span class="form-inline">

				<label><?php echo($this->items[0]->name." 契約容量");?></label>

                <input type="text" name="alarm_kw" value="<?php echo $alarm_kw;?>"/>

				<label style="width:200px">KW 當前用電 <?php echo($cur_kw);?> KW</label>


			</span>
	        <?php endif;?>
					<div id="mydiv">
			
          <canvas id="coinTapGame"></canvas>

          </div>

				<?php if($is_spacial_floor == false):?>
          <table style="width:100%;">
          <tr>

            <th width="100px">開始日期</th>
            <th width="100px">結束日期</th>
            <th width="100px">確認日期</th>
            <th width="30px">確認</th>
            <th width="100px">確認人員</th>
            <th width="120px">事件</th>
			<th width="100px">觸發原因</th>
			<th width="200px">連絡人</th>

          </tr>

          <?php foreach($devicelog as $i => $item):?>
            <tr id="mylog<?php echo $item->id;?>" bgcolor="<?php echo FloorHelpersFloor::setbgcolor($item);?>"  ondblclick="mydbClick(this,<?php echo$item->id;?>)" onclick="logClick(this,<?php echo$item->id;?>)">
            <td><?php echo $item->date.' '.$item->time;?></td>
            <td id="mylogendtd<?php echo $item->id;?>"><?php echo $item->end_date.' '.$item->end_time;?></td>
            <td id="mylogtd<?php echo $item->id;?>"><?php echo $item->check_date.' '.$item->check_time;?></td>
            <td><input type="checkbox" id="enable<?php echo $item->id;?>" value="<?php echo FloorHelpersFloor::is_checked($item);?>" <?php if(FloorHelpersFloor::is_checked($item) == true) echo ("checked");?>></td>

            <td id="checknametd<?php echo $item->id;?>"><?php echo $item->check_name;?></td>

            <td><?php echo $item->name_note;?></td>

			<td><?php echo $item->type;?></td>
			<td><?php echo $item->note;?></td>

            </tr>

          <?php endforeach;?>

        </table>
			<?php endif;?>
        </div>
      </div>
      <?php

      if(count($devices))
          $mypoints1 = FloorHelpersFloor::adjust_points($devices);

      ?>

      <?php foreach ($mypoints1 as $key => $value): ?>
      <?php

        if($value->status == 1)
        {

            $path = $value->green;
        }
        else if($value->status == 2)
        {

            $path = $value->yellow;
        }
        else
        {

            $path = $value->red;
        }


        $mycss = "myicon1";

      $click = '';

      if($value->dio_id > 0 && $value->dio_type == 2)
      {
        $click = 'onclick="objClick('."'".$value->id."','".$value->note."'".');"';
      }
      else if(FloorHelpersFloor::is_cctv($value))
      {
        $click = 'onclick="cctvClick('."'".$value->id."'".');"';

      //  $click = 'ondblclick="cctvClick('.$value->id.');"';
      }
			else if(FloorHelpersFloor::is_phone($value))
      {
        $click = 'ondblclick="phoneClick('."'".$value->info."'".');"';

      //  $click = 'ondblclick="cctvClick('.$value->id.');"';
      }

			$hidden = 'class="myshow"';
			if(empty($path))
					$hidden = 'class="myhidden"';

			?>

	<div  class="<?php echo $mycss;?>" id="txt<?php echo ($value->id);?>" align="center" <?php echo $click; ?>>
		<div <?php echo $hidden;?>>
			<div  style="width:<?php echo $value->width;?>px;height:<?php echo $value->height;?>px;">
			<?php if($is_spacial_floor && in_array($value->dio_type, FloorHelpersFloor::getElectronicMeterDioTypes())):?>
					<?php if($this->items[0]->elec_type == FloorHelpersFloor::$public_area):?>
             			<table>
							<tr>
								<?php if ($value->status == 3): ?>
									<th colspan="2" style="background-color:#FF9999;">
								<?php else: ?>
									<th colspan="2">
								<?php endif; ?>
									<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=electronicmeter&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a>
								</th>
							</tr>
							<tr>
								<td>
									<p><?php echo('RS-電壓');?></p>
									<p><?php echo('ST-電壓');?></p>
									<p><?php echo('TR-電壓');?></p>
									<p><?php echo('R-電流');?></p>
									<p><?php echo('S-電流');?></p>
									<p><?php echo('T-電流');?></p>
									<p><?php echo('功因');?></p>
									<p><?php echo('用電量');?></p>
									<p><?php echo('虛功率');?></p>
									<p><?php echo('用電度數');?></p>
								</td>
								<td>
									<p><?php echo($value->elec_v);?>V</p>
									<p><?php echo($value->elec_v_bn);?>V</p>
									<p><?php echo($value->elec_v_cn);?>V</p>
									<p><?php echo($value->elec_a_a);?>A</p>
									<p><?php echo($value->elec_a_b);?>A</p>
									<p><?php echo($value->elec_a_c);?>A</p>
									<p><?php echo($value->elec_pf);?></p>
									<p><?php echo($value->elec_kw);?>KW</p>
									<p><?php echo($value->elec_kvar);?>KVAR</p>
									<p><?php echo($value->elec_kwh);?>KWH</p>
								</td>
							</tr>
						</table>
					<?php else:?>
						<span style="font-size:large;">
							<font class="endointNodeText" color="<?php echo $value->color;?>" size="<?php echo $value->fontsize;?>">
						
								<!-- <a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_elec&view=roots&id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a> -->
								<!-- <a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=electronicmeter&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a> -->
								<?php if (in_array($value->dio_type, FloorHelpersFloor::getElectronicMeterDioTypes())): ?>

									<br/>
									<?php echo('累計用電：'.$value->elec_kwh.' kWh'); ?><br/>
									<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=electronicmeter&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a>
								<?php elseif  (in_array($value->dio_type, FloorHelpersFloor::getSolarMeterDioTypes())): ?>
									<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=solarmeter&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a>
									
									<br/>
									<?php echo('累計發電：'.$value->elec_kwh.' kWh'); ?>
								<?php else: ?>
									<?php if (strlen($value->note) > 0) :?>
										<?php echo $value->note;?>
									<?php endif; ?>
								<?php endif; ?>	
							</font>
						</span>
					<?php endif;?>
			<?php endif;?>
			</div>

				<?php if ((strlen($value->note) > 0 && !(in_array($value->dio_type, FloorHelpersFloor::getElectronicMeterDioTypes())))): ?>
					<p> 
					<?php if ($value->show_node_text): ?>
						<font class="endointNodeText" color="<?php echo $value->color;?>" size="<?php echo $value->fontsize;?>">
							
							<?php if (in_array($value->dio_type, FloorHelpersFloor::getWaterMeterDioTypes())): ?>
								<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_water_meter_history&view=watermeterhistory&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a>
							<?php elseif  (in_array($value->dio_type, FloorHelpersFloor::getSolarMeterDioTypes())): ?>
								<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=solarmeter&device_id=".(int)$value->id, false, 2) ?>"><?php echo($value->info);?></a>
							<?php else: ?>
								<?php echo $value->note;?>
							<?php endif; ?>
							<!-- <span id="txt_span_<?php echo $value->id; ?>"></span> -->
						</font>
						<br>
					<?php endif; ?>
					<font class="endointNodeText" color="<?php echo $value->color;?>" size="<?php echo $value->fontsize;?>"  id="txt_span_<?php echo $value->id; ?>"></font>
				
					</p>
				<?php endif; ?>
				<?php if (!$is_spacial_floor && in_array($value->dio_type, FloorHelpersFloor::getElectronicMeterDioTypes())): ?>
					<span style="font-size:large;">
							<?php if ($value->show_node_text): ?>
								<font class="endointNodeText" color="<?php echo $value->color;?>" size="<?php echo $value->fontsize;?>">
									<?php if (strlen($value->note) > 0): ?>
										<?php echo($value->note);?>
										<br/>
									<?php endif; ?>
									<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=electronicmeter&device_id=".(int)$value->id, false, 2) ?>">
										<?php echo('累計用電：'.$value->elec_kwh.' kWh'); ?>
									</a>								
									<!-- <br/>
									<a target="_blank" href="<?php echo JRoute::_("/index.php?option=com_electronic_meter_history&view=electronicmeterhistory&meter_type=electronicmeter&device_id=".(int)$value->id, false, 2) ?>">
										<?php echo($value->info);?>
										
									</a>								 -->
								
								</font>
								<br/>
							<?php endif; ?>
							<font class="endointNodeText" color="<?php echo $value->color;?>" size="<?php echo $value->fontsize;?>"  id="txt_span_<?php echo $value->id; ?>"></font>
							
						</span>
				<?php endif; ?>
		</div>
	</div>

    <?php endforeach;?>


		<!-- <div id="test_audio" ><?php var_dump($maxLogId[0]->alarm_sound_file); ?></div> -->
		<audio id="myalarm">
		 
		  <!-- <source src="<?php echo($accs[0]->alarm_path."/".$accs[0]->alarm_file);?>" type="audio/mpeg"> -->
		  <?php 
			$alarm_sound_file = "basic_alarm.mp3";
		  if (count($maxLogId) > 0)
		  {
				$alarm_sound_file = $maxLogId[0]->alarm_sound_file;
		  } 
		  ?>
		  <source src="<?php echo("images/alarm_audios/".$alarm_sound_file);?>" type="audio/mpeg">
		Your browser does not support the audio element.
		</audio>


	<input type="hidden" name="task" value=""/>
	<input type="hidden" name="boxchecked" value="0"/>
  <input type="hidden" name="is_alarm" id="is_alarm" value="0"/>
	<input type="hidden" name="is_save" id="is_save" value="0"/>
	<input type="hidden" name="group" value="<?php echo $mygroup;?>"/>
	<input type="hidden" name="id" id="id" value="<?php echo $myid;?>"/>
	<input type="hidden" name="filter_order" value="<?php echo $listOrder; ?>"/>
	<input type="hidden" name="filter_order_Dir" value="<?php echo $listDirn; ?>"/>
	<?php echo HTMLHelper::_('form.token'); ?>
</form>

  <style>
  .endointNodeText{
	  background-color: #e3e3e3;
	  padding: 2px;
	  border-radius: 3px;
  }
  .endointNodeText:empty{
	  display:none;
  }
.myhidden {
  display: none;
}
.myshow {
  display: block;
}

.myicon1
{

  //border:1px red solid;
  position:  absolute;
  left: 0;
  top: 0; /* set these so Chrome doesn't return 'auto' from getComputedStyle */
  background-size:cover;

  padding: 0px 0px 0px 0px;
}

  </style>


  <script type="text/javascript">
  <?php if(true || $is_spacial_floor == false):?>
	(function() {
		// http://paulirish.com/2011/requestanimationframe-for-smart-animating/
		// http://my.opera.com/emoller/blog/2011/12/20/requestanimationframe-for-smart-er-animating
		// requestAnimationFrame polyfill by Erik Möller. fixes from Paul Irish and Tino Zijdel
		// MIT license

	    var lastTime = 0;
	    var vendors = ['ms', 'moz', 'webkit', 'o'];
	    for(var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
	        window.requestAnimationFrame = window[vendors[x]+'RequestAnimationFrame'];
	        window.cancelAnimationFrame = window[vendors[x]+'CancelAnimationFrame']
	                                   || window[vendors[x]+'CancelRequestAnimationFrame'];
	    }

	    if (!window.requestAnimationFrame)
			{
				  //console.log("requestAnimationFrame");
	        window.requestAnimationFrame = function(callback, element) {
	            var currTime = new Date().getTime();
	            var timeToCall = Math.max(0, 16 - (currTime - lastTime));
	            var id = window.setTimeout(function() { callback(currTime + timeToCall); },
	              timeToCall);
	            lastTime = currTime + timeToCall;
	            return id;
	        };
      }
	    if (!window.cancelAnimationFrame)
	        window.cancelAnimationFrame = function(id) {
	            clearTimeout(id);
	        };
	}());

 coins = [];
(function () {

		var numCoins = <?php echo $number;?>,

			canvas;

			target = [

			<?php foreach ($devices as $key => $value): ?>
	<?php
			if($value->status == 1)
			{

					$path = $value->green;

			}
			else if($value->status == 2)
			{

					$path = $value->yellow;
			}
			else
			{

					$path = $value->red;
			}

	    if(empty($path))
			{
				$orgin_width = 200;
				$orgin_height = 100;
			}
			else
			    list($orgin_width, $orgin_height, $type, $attr) = getimagesize($path);
	?>

			//dm.setAttribute("style","display:block;width:30px");
	   {
			<?php if(empty($path)):?>
	    valid:0,
			<?php else:?>
			valid:1,
			<?php endif;?>
			orgin_width: <?php echo $orgin_width;?>,
	 		orgin_height:<?php echo $orgin_height;?>,

			width: <?php echo $value->width;?>,
			height:<?php echo $value->height;?>,
			path:"/<?php echo $path;?>",
			numberOfFrames:<?php echo($orgin_width/$orgin_height);?>,
			id:<?php echo $value->id;?>,
		},
			<?php endforeach;?>
		];

		function gameLoop () {

		  var i;
      //console.log("gameLoop");
		  window.requestAnimationFrame(gameLoop);

		  // Clear the canvas
		  canvas.getContext("2d").clearRect(0, 0, canvas.width, canvas.height);

		  for (i = 0; i < coins.length; i += 1) {
				if(coins[i].valid == 1)
				{
			      coins[i].update();
			      coins[i].render();
			  }
		  }
		}

		function sprite (options) {

			var that = {},
			frameIndex = 0,
			tickCount = 0,
			ticksPerFrame = options.ticksPerFrame || 0,
			numberOfFrames = options.numberOfFrames || 1;

			that.context = options.context;
			that.width = options.width;
			that.height = options.height;

			that.x = 0;
			that.y = 0;
			that.image = options.image;
			that.scaleRatio = 1;

			that.update = function () {

	       tickCount += 1;

	       if (tickCount > ticksPerFrame) {

					   tickCount = 0;

	           // If the current frame index is in range
	           if (frameIndex < numberOfFrames - 1) {
	               // Go to the next frame
	               frameIndex += 1;
	            } else {
	               frameIndex = 0;
	            }
	        }
	    };

			that.render = function () {

			  // Draw the animation
			  that.context.drawImage(
			    that.image,
			    frameIndex * that.width / numberOfFrames,
			    0,
			    that.width / numberOfFrames,
			    that.height,
			    that.x,
			    that.y,
					options.target_width,
					options.target_height);
			};

			that.getFrameWidth = function () {
				return that.width / numberOfFrames;
			};

			return that;
		}

		function destroyCoin (coin) {

			var i;

			for (i = 0; i < coins.length; i += 1) {
				if (coins[i] === coin) {
					coins[i] = null;
					coins.splice(i, 1);
					break;
				}
			}
		}

		function spawnCoin () {

			var coinIndex,
				coinImg;

			coinIndex = coins.length;

			// Create sprite sheet
			coinImg = new Image();

			// Create sprite
			coins[coinIndex] = sprite({
				context: canvas.getContext("2d"),
				width: target[coinIndex].orgin_width,
				height: target[coinIndex].orgin_height,
				image: coinImg,
				numberOfFrames: target[coinIndex].numberOfFrames,
				ticksPerFrame: 20,
				target_width:target[coinIndex].width,
				target_height:target[coinIndex].height
			});

      coins[coinIndex].valid = 1;
			coins[coinIndex].scaleRatio = 1;//Math.random() * 0.5 + 0.5;

      //console.log(target[coinIndex].path);
			// Load sprite sheet
			coinImg.src = target[coinIndex].path;

			if(target[coinIndex].valid == 0)
			{
				coins[coinIndex].valid = 0;
			}
		}

		// Get canvas
		canvas = document.getElementById("coinTapGame");
		canvas.width =<?php echo $width;?>;
		canvas.height = <?php echo $height;?>;

		for (i = 0; i < numCoins; i += 1) {

			spawnCoin();
		}

<?php foreach ($devices as $key => $value): ?>

//dm.setAttribute("style","display:block;width:30px");

coins[<?php echo $key?>].x = <?php echo $value->pointx;?>;
coins[<?php echo $key?>].y = <?php echo $value->pointy;?>;
coins[<?php echo $key?>].id = <?php echo $value->id;?>;
<?php endforeach;?>

		gameLoop();

	} ());
  <?php endif;?>
  var refresh_realtime_data_timeout = 3*1000;
  var refresh_license_timeout = 60*1000;
  var phoneCCTV_timeout = 2000;
  var timeout = 2000;
  var timeout_log_init = 3000;
	var timeout_info = 2000;
  var timeout_log = 1000;
  var home_timeout = 1000*300;
	var alarm_timeout = 4000;
	var login_timeout = 1000*120;


  <?php
		$max = 0;
		$latest_note5_equals_to_1_ids = FloorHelpersFloor::get_max_note5_equals_to_1_device_log_id();
		// $latest_note5_equals_to_1_ids = FloorHelperFloors::get_latest_device_logs(5);
		if (count($latest_note5_equals_to_1_ids) > 0) {
			$max = $latest_note5_equals_to_1_ids[0]->id;
		}
        // foreach($devicelog as $i => $item)
        // {
        //     if(($item->note5 == 1) && ($item->id > $max))
        //     {
        //       $max = $item->id;
        //     }
        // }

    ?>
  var m_max = <?php echo $max;?>;
  var m_logObj = [
  <?php foreach($devicelog as $i => $item):?>
      {
  id:<?php echo $item->id;?>,
  status:<?php echo $item->status;?>,
      },
  <?php endforeach;?>

  ];


  var m_myObj = [

  <?php foreach($mypoints1 as $i=>$item):?>
    {

      id:<?php echo $item->id;?>,
      dio_id:<?php echo $item->dio_id;?>,

      index:<?php echo $item->index;?>,
     status:<?php echo $item->status;?>,

     green:"<?php echo $item->green;?>",
		 yellow:"<?php echo $item->yellow;?>",
     red:"<?php echo $item->red;?>",
     name:"<?php echo $item->path;?>",
     cctv:<?php echo $item->open;?>,
		 group:<?php echo $item->group;?>,
		 web_view_subpath:"<?php echo $item->web_view_subpath;?>",
			web_view_port:"<?php echo $item->web_view_port;?>",
			web_view_scheme:"<?php echo $item->web_view_scheme;?>"
    },

    <?php endforeach;?>

  ];

	var m_cctvTop = [
	<?php foreach ($mycctvtop as $key => $item): ?>

		<?php
		    if($item->username=="")
            continue;
		?>
		{
		id:<?php echo $item->id;?>,
		username:"<?php echo $item->username;?>",
		passwd:"<?php echo $item->password;?>",
	},
	<?php endforeach;?>
  ];
  var m_cctvObj = [

  <?php foreach($mycctv as $i=>$item):?>
    {
			dio_id:<?php echo $item->dio_id;?>,
      id:<?php echo $item->id;?>,
     name:"<?php echo $item->path;?>",
      open:<?php echo $item->open;?>,
			path:"<?php echo $item->path;?>",
			open1:<?php echo $item->open1;?>,
			path1:"<?php echo $item->path1;?>",
			open2:<?php echo $item->open2;?>,
			path2:"<?php echo $item->path2;?>",
			open3:<?php echo $item->open3;?>,
			path3:"<?php echo $item->path3;?>",
			web_view_subpath:"<?php echo $item->web_view_subpath;?>",
			web_view_port:"<?php echo $item->web_view_port;?>",
			web_view_scheme:"<?php echo $item->web_view_scheme;?>",
			
			web_view_subpath1:"<?php echo $item->web_view_subpath1;?>",
			web_view_port1:"<?php echo $item->web_view_port1;?>",
			web_view_scheme1:"<?php echo $item->web_view_scheme1;?>",
			
			web_view_subpath2:"<?php echo $item->web_view_subpath2;?>",
			web_view_port2:"<?php echo $item->web_view_port2;?>",
			web_view_scheme2:"<?php echo $item->web_view_scheme2;?>",
			
			web_view_subpath3:"<?php echo $item->web_view_subpath3;?>",
			web_view_port3:"<?php echo $item->web_view_port3;?>",
			web_view_scheme3:"<?php echo $item->web_view_scheme3;?>",
			group:<?php echo $item->group;?>
    },

    <?php endforeach;?>

  ];
  var m_cctvObj1 = [

<?php foreach($mycctv1 as $i=>$item):?>
  {
		  dio_id:<?php echo $item->dio_id;?>,
	id:<?php echo $item->id;?>,
   name:"<?php echo $item->path;?>",
	open:<?php echo $item->open;?>,
		  path:"<?php echo $item->path;?>",
		  open1:<?php echo $item->open1;?>,
		  path1:"<?php echo $item->path1;?>",
		  open2:<?php echo $item->open2;?>,
		  path2:"<?php echo $item->path2;?>",
		  open3:<?php echo $item->open3;?>,
		  path3:"<?php echo $item->path3;?>",
		  web_view_subpath:"<?php echo $item->web_view_subpath;?>",
		  web_view_port:"<?php echo $item->web_view_port;?>",
		  web_view_scheme:"<?php echo $item->web_view_scheme;?>",
		  
		  web_view_subpath1:"<?php echo $item->web_view_subpath1;?>",
		  web_view_port1:"<?php echo $item->web_view_port1;?>",
		  web_view_scheme1:"<?php echo $item->web_view_scheme1;?>",
		  
		  web_view_subpath2:"<?php echo $item->web_view_subpath2;?>",
		  web_view_port2:"<?php echo $item->web_view_port2;?>",
		  web_view_scheme2:"<?php echo $item->web_view_scheme2;?>",
		  
		  web_view_subpath3:"<?php echo $item->web_view_subpath3;?>",
		  web_view_port3:"<?php echo $item->web_view_port3;?>",
		  web_view_scheme3:"<?php echo $item->web_view_scheme3;?>",
		  group:<?php echo $item->group;?>
  },

  <?php endforeach;?>

];
	var m_menuObj = [
  <?php
	   $items = FloorHelpersFloor::getMenus();
	   foreach($items as $i=>$item):?>
    {
			link:'<?php echo $item->link;?>',
      menu:'<?php echo JRoute::_($item->link, false, 2) ?>',
			alias:'<?php echo $item->alias;?>',

    },

    <?php endforeach;?>

  ];

	var msgUrl = '<?php echo JRoute::_('index.php?option=com_floor&task=roots.show_msg', false, 2)?>';

  var m_winid=[];
	var m_phoneCCTVObj=[];
	var phone_min=<?php echo($phone_min);?>;
	var phone_max=<?php echo($phone_max);?>;
	<?php 
		$maxLogId = FloorHelpersFloor::findMaxLogId();
		$needAlarm = count($maxLogId) ?1:0;
	?>
  var is_alarm = <?php echo $needAlarm;?>;


    <?php if($canCreate) : ?>

    window.onbeforeunload = confirmExit;
    function confirmExit()
    {
      closeCCTVWindow();
      return;//"You have attempted to leave this page.  If you have made any changes to the fields without clicking the Save button, your changes will be lost.  Are you sure you want to exit this page?";
    }

    function show_cctv(dev,index)
    {

      if(index == 0)
			{
				if(dev.open!=null)
				{
					cctv = 1;
				}
				else
				{
					cctv = dev.open;
				}
				
				path = dev.path;
				mleft = 0;
				mtop = 0;
			}
			else if(index == 1)
			{
				cctv = dev.open1;
				path = dev.path1;
				mleft = 600;
				mtop = 0;

			}
			else if(index == 2)
			{
				cctv = dev.open2;
				path = dev.path2;
				mleft = 0;
				mtop = 600;

			}
			else if(index == 3)
			{
				cctv = dev.open3;
				path = dev.path3;

				mleft = 600;
				mtop = 600;

			}

			if(cctv)
			{
				  myid = dev.id;
					name = dev.name;
					if(dev.dio_id > 0)
							name = path;

				 jQuery.each(m_cctvTop, function(index1, value1) {
									 if(value1.id == dev.group)
									 {
											// name = value1.username+":"+value1.passwd+"@"+name;
											// name = dev.web_view_scheme + "://" + value1.username + ":" + value1.passwd + "@" + name + ":" + dev.web_view_port + dev.web_view_subpath;
											 //console.log("ready "+name);
											 //break;
									 }

					});
					var fullUrlPath = dev.web_view_scheme + "://" + name + ":" + dev.web_view_port + dev.web_view_subpath;
					// alert(dev.id,dev.web_view_port + dev.web_view_scheme + dev.web_view_subpath);
					console.log("YOOOOOOOOOOOOOO",dev);
					// alert(fullUrlPath);
					console.log("ready "+fullUrlPath);
					let url;
					try {
						url = new URL(fullUrlPath);
					} catch (_) {
						return false;
					}
					//break;
					console.log(is_window_valid(fullUrlPath));
					if(is_window_valid(url) == false)
					{
	            id = window.open(url, '', 'height=600,width=600,left='+mleft+',top='+mtop);
					    var myhand = new Object();
					    myhand.id = id;
					    myhand.name = url;
							myhand.mydev = myid;
	            m_winid.push(myhand);

	            console.log("1"+url);
	            console.log(mleft+" "+mtop);
				  }

			}

    }
  	jQuery(document).ready(function () {

      console.log("1234 "+'<?php echo $myid;?> '+'<?php echo $mygroup;?> ');

      <?php if(count($infos)):?>
			    var arr = {message:'<?php echo($infos[0]->message)?>'};
				
          popup(arr);
			<?php endif;?>

			jQuery("#save1").click(saveItem);

      set_icons();

      statusFunc();
	 // getPhoneCCTVDevices();
			phoneCCTVStatusFunc();
			getAuthenticationStatus();
			loopLicenseNotification();
			getRealtimeDeviceData();
			getRealtimeDeviceDataByFloorId();
			setTimeout(loginStatusFunc, login_timeout);

			playFunc();

			//console.log(m_cctvObj);
      jQuery.each(m_cctvObj, function(index, value) {
           show_cctv(value,0);
					 show_cctv(value,1);
					 show_cctv(value,2);
					 show_cctv(value,3);

      });

	  setTimeout(callccTv, 1000);
	  setTimeout(callclosewindow, 1000);
	  setTimeout(checkIsChangeSettingDone, 1000);
	  
      setTimeout(gohome, home_timeout);
      setTimeout(getLogStatus, timeout_log_init);
			setTimeout(get_info_msg, timeout_info);
	  		// setTimeout(getRealtimeDeviceData, refresh_realtime_data_timeout);
  	});
	  var callccTvdata=[];
	  function callccTv() {
		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getOpenDevices'); ?>";
		jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          success: function(response) {
			callccTvdata = JSON.parse(response);
			  if(JSON.stringify(m_cctvObj1) !== JSON.stringify(callccTvdata))
			  {
				console.log("ok");
		  window.location.reload();
			}
			setTimeout(callccTv, 1000);
          },
          error: function(response) {
              console.log("error4");
          }
      });

	  }
	  function callclosewindow() {
		var temp;
		var delay = 1000;
		var id;
		let today = new Date();
		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.call_window_close'); ?>";
		jQuery.ajax({
        url: alprUrl,
        headers: {
		"Content-Type": "application/json"
		},
		type: "POST",
		// timeout:10000,
		success: function(response) {
			temp = response;
		//	console.log("dalay:"+temp);
			if(temp==1)
			{
				jQuery.each(m_winid, function(index, value) {
					id = value.id;
				})
				//console.log("dalay:"+delay);
				if(id)
				{
				id.close();
				setTimeout(funcx,delay);
				}
			
			}
			setTimeout(callclosewindow, delay);
        },
    	error: function(response) {
        	console.log("error4");
			setTimeout(callclosewindow, delay);
        }
		});

		
	  }
	  
	  function checkIsChangeSettingDone() {
		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.checkIsChangeSettingDone'); ?>";
		jQuery.ajax({
        url: alprUrl,
		// timeout:10000,
        headers: {
		"Content-Type": "application/json"
		},
		type: "POST",
		success: function(response) {
			setTimeout(checkIsChangeSettingDone, 1000);
        },
    	error: function(response) {
        	console.log("error4");
			setTimeout(checkIsChangeSettingDone, 1000);
        }
		});
		
	  }
		function saveItem() {

		    //alert('1111');
		    form = document.getElementById('adminForm');

	      form.is_save.value = 1;

	        // Submit the form.
	        if (typeof form.onsubmit == 'function') {
	            form.onsubmit();
	        }
	        if (typeof form.fireEvent == "function") {
	            form.fireEvent('onsubmit');
	        }

	        //Joomla.submitbutton('roots.add1');
	        form.submit();

	  }

		function phoneCCTVItem(id) {

      form = document.getElementById('adminForm');

      form.id.value = id;

      // Submit the form.
      if (typeof form.onsubmit == 'function') {
            form.onsubmit();
      }
      if (typeof form.fireEvent == "function") {
            form.fireEvent('onsubmit');
      }

      form.submit();

  	}

    function gohome() {
      ///window.location.href = '<?php echo JRoute::_('index.php?option=com_whome&view=roots'); ?>';

    }

		function run_logout() {

      <?php $userToken = JSession::getFormToken();?>
			window.location.href = "index.php?option=com_users&task=user.logout&<?php echo $userToken?>=1";

		}
    function do_device_state(obj)
		{
			is_found = false;

			jQuery.each(m_myObj, function(index, value) {
				  if(obj.node_id == value.id)
              is_found = true;

      });


			if(is_found == false)
			{
          alarm_funcx(obj);
			}
			else
			{
				funcx();
			}

		}
		function getDeviceFunc(mid) {

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getDevice'); ?>";

			var myId = {id:mid};

			//myvar = {};
			var jsonArray = JSON.stringify(myId);

      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
						  console.log("getDeviceFunc");
              console.log(response);

              var arr = JSON.parse(response);
							//console.log(arr[0]);
              if(arr[0].type != 1)
							{
                  cctvShow(arr[0]);
              }
              //setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error4");
              //setTimeout(statusFunc, timeout*10);

          }
      });

    }

		function statusFunc() {

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getStatus'); ?>";

      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: "",//jsonArray,
          success: function(response) {
              //console.log(response);

              var arr = JSON.parse(response);
              //console.log(arr);
							changeStatus(arr);

              setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error5");
              setTimeout(statusFunc, timeout*10);

          }
      });

    }

    function getPhoneCCTVDevices()
		{
			<?php
			     $arrs = FloorHelpersFloor::getPhoneCCTVStatus();
			?>
			<?php  foreach($arrs as $i=>$item):?>
			//value2.path = "www.yahoo.com.tw";
			<?php
			$mypath = $item->path;
			foreach($mycctvtop as $i1=>$item1)
			{
          if(($item1->username != "") && $item->group == $item1->id)
					{
							$mypath = $item1->username.':'.$item1->password.'@'.$mypath;
							break;
					}
			}
			?>

			id = window.open("http://"+'<?php echo($mypath);?>', '', config='height=600,width=600');

			var myObj = new Object();
			myObj.open = <?php echo($item->open);?>;
			myObj.winid = id;
			myObj.id = <?php echo($item->id);?>;
			m_phoneCCTVObj.push(myObj);
			console.log("open cctv "+'<?php echo($item->path);?>');

			<?php endforeach;?>

		}
    function phoneCCTVStatusFunc() {

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getPhoneCCTVStatus'); ?>";

      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: "",//jsonArray,
          success: function(response) {
              //console.log(response);

              var arr = JSON.parse(response);
							//console.log(arr);
              showPhoneCCTV(arr);

              setTimeout(phoneCCTVStatusFunc, phoneCCTV_timeout);
          },
          error: function(response) {
              console.log("error6");
              setTimeout(phoneCCTVStatusFunc, phoneCCTV_timeout*10);

          }
      });

    }
		function loginStatusFunc() {

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.check_login'); ?>";

			var myvar = {name:'<?php echo($name);?>'};

      //myvar = {};
      var jsonArray = JSON.stringify(myvar);

      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
              //console.log(response);

              var arr = JSON.parse(response);
							console.log(arr);
              if(arr == true)
							{
                  run_logout();
							}
							else
                 setTimeout(loginStatusFunc, login_timeout);
          },
          error: function(response) {
              console.log("error6");
              setTimeout(loginStatusFunc, login_timeout*10);

          }
      });

    }

		function pop(div) {
		    document.getElementById(div).style.display = 'block';
		}
		function hide(div) {
		    document.getElementById(div).style.display = 'none';
		}

    function popup(arr)
		{

        n = msgUrl.indexOf("index.php");
				msg = msgUrl.substr( 0 , n )+"index.php?option=com_floor&task=sroots.show_msg&msg="+arr.message;

        console.log(msg);
				window.open(msg, '', 'height=200,width=400,left=0,top=0');

		}
    function get_info_msg()
    {

      //console.log('get_info_msg');
      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.get_info_msg'); ?>";

      //console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          //data: jsonArray,
          success: function(response) {

              //console.log(response);
              var arr = JSON.parse(response);
              //console.log(arr);

              if(arr != null && arr.length > 0)
							{
								  popup(arr[0]);
                  //alert(arr[0].message);
							}

              setTimeout(get_info_msg, timeout_info);

          },
          error: function(response) {
              console.log("error get_info_msg");
              setTimeout(get_info_msg, timeout_info);

          }
      });

    }
	function getRealtimeDeviceDataByFloorId()
	{
		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getRealtimeDeviceDataByFloorId').'?floor_id='.$myid; ?>";		
		jQuery.ajax({
			url: alprUrl,
			headers: {
				"Content-Type": "application/json"
			},
			type: "POST",
			timeout: 5000,
			data: "",
			success: function(response) {
				var responseObj = null;
				try {
					responseObj = JSON.parse(response);
				} catch (err) {
					console.log("getRealtimeDeviceDataByFloorId JSON parse error", err);
					setTimeout(getRealtimeDeviceDataByFloorId, refresh_realtime_data_timeout);
					return;
				}
				response = responseObj;
				// Reset all building/floor alarm badges and styles to avoid stale values lingering
				jQuery('.building_alarm_count').text('');
				jQuery('.building_alarm_count').removeClass('alarm_node');
				jQuery('.floor_alarm_count').text('');
				jQuery('.floor_alarm_count').removeClass('alarm_node');
				// Only rebuild menu badges after successful parse; prevent removal without redraw on parse failure
				jQuery('.alarm_menu').remove();
				var alarm_nodes = jQuery('.alarm_node');
				alarm_nodes.removeClass('alarm_node');
				response.allMenuAlarmCount.forEach(menuAlarmCount=> {
					var element = jQuery('li').find('.main-menu-' + menuAlarmCount.id);
					if (element.length > 0)
					{
						element.data('sum',0);
					}
				});
				response.allMenuAlarmCount.forEach(menuAlarmCount=> {
					if (menuAlarmCount.count > 0)
					{
						var element = jQuery('li').find('.main-menu-' + menuAlarmCount.id);
						if (element.length > 0)
						{
							if (!element.eq(0).hasClass('parent'))
							{
								var parents = element.parents('li').find('.parent');
								if (parents.length > 0)
								{
									var sum = parents.eq(0).data('sum');
									if (sum == undefined)
									{
										sum = 0;
									}
									sum += menuAlarmCount.count;
						
									parents.eq(0).data('sum',sum);
	
								}

							}
							element.eq(0).append("<text class='alarm_menu'>(" + menuAlarmCount.count + ")</text>");
						
						}

					}
				});
				response.allMenuAlarmCount.forEach(menuAlarmCount=> {
					var element = jQuery('li').find('.main-menu-' + menuAlarmCount.id);
					if (element.length > 0)
					{							
						var sum = element.eq(0).data('sum');
						if (sum != undefined && sum > 0)
						{								
							element.children('.alarm_menu').remove();
							element.eq(0).append("<text class='alarm_menu'>(" + sum + ")</text>");
						}
					}
				
				});
				response.allFloorAlarmCount.forEach(floorAlarmCount => {
					var element = jQuery('#floor_alarm_count_' + floorAlarmCount.floor);
					if (element.length > 0)
					{
						// console.log(element);
						element.text("(" + floorAlarmCount.alarm_count +")");
						element.addClass('alarm_node');
					}
				});
				response.allBuildingAlarmCount.forEach(buildingAlarmCount => {
					var element = jQuery('#building_alarm_count_' + buildingAlarmCount.building);
					if (element.length > 0)
					{
						// console.log(element);
						element.text("(" + buildingAlarmCount.alarm_count +")");
						element.addClass('alarm_node');
					}
				});
				// jQuery(".floor_alarm_count").forEach(element => {
				// 	if (element.text() == '')
				// 	{
				// 		element.removeClass('alarm_node');
				// 	}
				// });
				response.deviceData.forEach(device => {
					let device_id = device.id;
					if (device.dio_type == 999 && device.info.includes("C$"))
					{
						device_id = parseInt(device.info.replace("C$",""));
					}
					var deviceSpanNodes = jQuery("#txt" + device_id);
					if (deviceSpanNodes.length > 0){
						var deviceSpanNode = deviceSpanNodes.eq(0);
						// var human_readable_time_duration = humanizeDuration(new Date() - new Date(device.update + " " + device.update_time), {language: "zh_TW", round:true});
						// var human_readable_last_change_duration = humanizeDuration(new Date() - new Date(device.update + " " + device.update_time), {language: "zh_TW" , round:true});
						// var elapsed = "\n持續時長：" + human_readable_time_duration;
						var last_change_time = "\n變更時間：" + device.update + " " + device.update_time;
						var last_alarm_duration = "";
						if (device.display_last_alarm_time == 1)
						{
							if (device.status == 1)
							{
								last_alarm_duration += "\n運轉時間：" + humanizeDuration(new Date() - new Date(device.last_alarm_time), {language: "zh_TW", round:true});
							}
							else if (device.status == 3)
							{
								last_alarm_duration += "\n運轉時間：" + humanizeDuration(new Date(device.update + " " + device.update_time) - new Date(device.last_alarm_time), {language: "zh_TW", round:true});
							}
						}
						var power_info = '';
						if (device.dio_type == 24)
						{
							power_info += '-----能源資訊-----\n電壓：' +  device.elec_v + ' V\n電流：' + device.elec_a_a + ' A\n功率：' + device.elec_kw + ' kW\n累計用量：' + device.elec_kwh + ' kWh\n';
							power_info += '溫度1：' + device.smart_lamp_temperature1 + '°C\n';
							power_info += '溫度2：' + device.smart_lamp_temperature2 + '°C\n';
							power_info += '溫度3：' + device.smart_lamp_temperature3 + '°C\n';
							power_info += '溫度4：' + device.smart_lamp_temperature4 + '°C\n';
							power_info += '類型：' + device.text_value + '\n';
							power_info += '版本：' + device.decimal_value + '\n';
						}
						if (device.dio_type == 25)
						{
							power_info += '-----能源資訊-----\nRS-電壓：' +  device.elec_v + ' V\nST-電壓：' + device.elec_v_bn + ' V\nTR-電壓：' + device.elec_v_cn + ' V\nR-電流：' + device.elec_a_a + ' A\nS-電流：' + device.elec_a_b + ' A\nT-電流：' + device.elec_a_c + ' A\n功率：' + device.elec_kw + ' kW\n累計用量：' + device.elec_kwh + ' kWh\n';
							power_info += '溫度1：' + device.smart_lamp_temperature1 + '°C\n';
							power_info += '溫度2：' + device.smart_lamp_temperature2 + '°C\n';
							power_info += '溫度3：' + device.smart_lamp_temperature3 + '°C\n';
							power_info += '類型：' + device.text_value + '\n';
							power_info += '版本：' + device.decimal_value + '\n';
						}
						if (device.dio_type == 28)
						{
							power_info += '-----空氣品質資訊-----\n';
							power_info += '溫度：' + device.temp + '°C\n';
							power_info += '濕度：' + device.humidity + '％\n';
							power_info += 'CO：' + device.co + ' ppm\n';
							power_info += 'CO2：' + device.co2_ppm + ' ppm\n';
							power_info += 'TVOC：' + device.tvoc + ' ppb\n';
							power_info += 'HCHO：' + device.hcho + ' ppb\n';
							power_info += 'PM1.0：' + device.pm01 + ' ug/m3\n';
							power_info += 'PM2.5：' + device.pm25 + ' ug/m3\n';
							power_info += 'PM10：' + device.pm10 + ' ug/m3\n';
						}
						if (device.dio_type == 38)
						{
							power_info += '-----空氣品質資訊-----\n';
							power_info += '溫度：' + device.temp + '°C\n';
							power_info += '濕度：' + device.humidity + '％\n';
						}
						if (device.dio_type == 39)
						{
							power_info += '-----空氣品質資訊-----\n';
							power_info += '溫度：' + device.temp + '°C\n';
							power_info += '濕度：' + device.humidity + '％\n';
							power_info += 'CO2：' + device.co2_ppm + ' ppm\n';
						}
						if (device.dio_type == 40)
						{
							power_info += '-----空氣品質資訊-----\n';							
							power_info += 'CO：' + device.co + ' ppm\n';
						}
						if (device.dio_type == 41)
						{
							power_info += '-----空氣品質資訊-----\n';
							power_info += 'PM2.5：' + device.pm25 + ' ug/m3\n';
							power_info += 'PM10：' + device.pm10 + ' ug/m3\n';
						}
						if (device.dio_type == 42)
						{
							power_info += '-----空氣品質資訊-----\n';
							power_info += '溫度：' + device.temp + '°C\n';
							power_info += '照度：' + device.lux + ' ug/m3\n';
						}
						if (device.dio_type == 999 && device.info.includes("E$"))
						{
							var nodes = jQuery("#txt_span_" + device.id);
							if (nodes.length > 0){
								var node = nodes.eq(0);								
								node.html("" + device.elec_kw + " Kw");
							}
							// var deviceSpanNodes = jQuery("#txt_span_" + device.id);
							// if (deviceSpanNodes.length > 0){
							// 	var deviceSpanNode = deviceSpanNodes.eq(0);
							// }
						}
						deviceSpanNode.attr('title', power_info + "-----系統資訊-----\nname: " + device.note + "\ninfo: " + device.info + "\nid:" + device.id + ", dio_type:" + device.dio_type + ", status:" + device.status + ", trigger_alarm:" + device.trigger_alarm + last_change_time + last_alarm_duration );
						
					}
				});
				// response.coSensorData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/>" + device.co + "ppm");
				// 	}
				// });
				// response.temperatureSensorData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/> 溫度：" + device.temp + "度<br/>濕度：" +  + device.humidity + "%");
				// 		if (device.dio_type == 18)
				// 		{
				// 			deviceSpanNode.html(deviceSpanNode.html() + "<br/>" + "CO2:" + device.co2_ppm + " ppm");
				// 		}
				// 	}
				// });
				// response.waterMeterData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/> 水量積算：" + device.accumulateWaterFlow + "度");
				// 	}
				// });
				// response.liuchuanElevatorData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/> 樓層：" + device.liuchuan_floor_display_name + "<br/>");
				// 	}
				// });
				// response.mitsubishiElevatorData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/> 樓層：" + device.mitsubishi_floor_display_name + "<br/>");
				// 	}
				// });
				// response.smartLampData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/>" + device.voltage + " V<br/>" + device.current + " A<br/>" + device.power +  " W<br/>" + device.accumulatePower + " W/H");
				// 	}
				// });
				// response.jetecWindDirectionData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/>" + device.text_value);
				// 	}
				// });
				// response.jetecRainMeterData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/>數值：" + device.text_value);
				// 	}
				// });
				// response.jetecSoilMeterData.forEach(device => {
				// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// 		deviceSpanNode.html("<br/>濕度：" + device.text_value + "％");
				// 	}
				// });
				// // response.electronicMeterData.forEach(device => {
				// // 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
				// // 	if (deviceSpanNodes.length > 0){
				// // 		var deviceSpanNode = deviceSpanNodes.eq(0);
				// // 		deviceSpanNode.html("<br/>累計用電：" + device.elec_kwh + "度");
				// // 	}
				// // });
				setTimeout(getRealtimeDeviceDataByFloorId, refresh_realtime_data_timeout);
			},
			error: function(response) {
				console.log("getRealtimeDeviceDataByFloorId Error");
				setTimeout(getRealtimeDeviceDataByFloorId, refresh_realtime_data_timeout);

			}
		});
	}
	var notificationString = "";
	var notificationEnabled = false;
	var loopLicenseNotification = function() {
		if (notificationEnabled)
		{
			toastr.error('<h1>' + notificationString + '</h1>');
		}
		// console.log('try show license notification' , notificationEnabled);
		setTimeout(loopLicenseNotification , 1000);
	};
	function getAuthenticationStatus()
	{
		// http://192.168.99.49/index.php?option=com_site_settings&task=get.getlicensestatus
		alprUrl = "<?php echo JRoute::_('index.php?option=com_site_settings&task=get.getlicensestatus'); ?>";		
		jQuery.ajax({
			url: alprUrl,
			headers: {
				"Content-Type": "application/json"
			},
			type: "GET",
			data: "",
			success: function(response) {
// 				{
// ActivationExpired: true,
// NotificationStarted: true,
// ForceStoped: true
// }
				// console.log(response);
				var responseObj = JSON.parse(response);
				// if (res)
				console.log(responseObj.license.SiteId);

				if (!responseObj.license.SiteId.startsWith('PENGHU'))
				{
					return;
				}
				// if (responseObj.ForceStoped)
				// {
				// 	jQuery('*').removeProp('onclick');
				// 	jQuery('*').removeProp('dbclick');
				// 	jQuery('*').off('click');
				// 	jQuery('*').off('dbclick');
				// 	jQuery('*').unbind('click');
				// 	jQuery('*').unbind('dbclick');
				// }
				if (responseObj.ActivationExpired)
				{
					if (jQuery("#licenseOverlay").length > 0)
					{
						jQuery("#licenseOverlay").eq(0).show();
					}
					else
					{
						jQuery('body').prepend('<div id="licenseOverlay"></div>');
					}
				}
				else
				{
					if (jQuery("#licenseOverlay").length > 0)
					{
						jQuery("#licenseOverlay").eq(0).hide();						
					}
				}
				if (responseObj.ForceStoped)
				{
					jQuery("#licenseOverlay").css('pointer-events','auto');
					console.log('forcestop');
				}
				else
				{
					jQuery("#licenseOverlay").css('pointer-events','none');
					console.log('non-force-stop');
				}
				if (responseObj.NotificationStarted)
				{					
					console.log('notification start');
					notificationEnabled = responseObj.NotificationStarted;
		
					toastr.options = {
						// 參數設定[註1]
						"closeButton": false, // 顯示關閉按鈕
						"debug": false, // 除錯
						"newestOnTop": false,  // 最新一筆顯示在最上面
						"progressBar": false, // 顯示隱藏時間進度條
						"positionClass": "toast-bottom-center", // 位置的類別
						"preventDuplicates": false, // 隱藏重覆訊息
						"onclick": null, // 當點選提示訊息時，則執行此函式
						"showDuration": "300", // 顯示時間(單位: 毫秒)
						"hideDuration": "1000", // 隱藏時間(單位: 毫秒)
						"timeOut": "6000", // 當超過此設定時間時，則隱藏提示訊息(單位: 毫秒)
						"extendedTimeOut": "0", // 當使用者觸碰到提示訊息時，離開後超過此設定時間則隱藏提示訊息(單位: 毫秒)
						"showEasing": "swing", // 顯示動畫時間曲線
						"hideEasing": "linear", // 隱藏動畫時間曲線
						"showMethod": "fadeIn", // 顯示動畫效果
						"hideMethod": "fadeOut" // 隱藏動畫效果
					}
					notificationString = responseObj.NotificationString;
					if (responseObj.ForceStoped) {
						notificationString = responseObj.ForceStopString;																	
					}
						// setInterval(() => {
						// 	if (notificationEnabled)
						// 	{
						// 		toastr.error('<h1>' + msg + '</h1>');
						// 	}
						// }, 1000);
						// jQuery('body').prepend('<h1 class="licenseNotification" style="top:42%;">請檢查連線狀態。</h1>');								
						
						// for (let i = 0; i < 5; i++) {
						// 	var msg = responseObj.NotificationString;
						// 	if (responseObj.ForceStoped) {
						// 		msg = responseObj.ForceStopString;																	
						// 	}
						// 	// jQuery('body').prepend('<h1 class="licenseNotification" style="animation: longblink ' + animationDuration + 's forwards infinite '+ delay + 's;top:' + top + '%;">' + msg + '</h1>');
						// 	jQuery('body').prepend('<h1 class="licenseNotification" style="animation: longblink ' + animationDuration + 's  infinite '+ delay + 's;top:' + top + '%;">' + msg + '</h1>');
						// 	top = top + 10;
						// 	delay = delay + animationGap;
						// 	// animation-delay:.3s;	
						// }


						// var top = 25;
						// var delay = 0;
						// var animationDuration = 10;
						// var animationGap = 1;
						// var i = 0;
						// setInterval(function(){
						// 	if (++i %6 != 0)
						// 	{
						// 		var msg = responseObj.NotificationString;
						// 		if (responseObj.ForceStoped) {
						// 			msg = responseObj.ForceStopString;																	
						// 		}
						// 		// jQuery('body').prepend('<h1 class="licenseNotification" style="animation: longblink ' + animationDuration + 's forwards infinite '+ delay + 's;top:' + top + '%;">' + msg + '</h1>');
						// 		jQuery('body').prepend('<h1 class="licenseNotification" style="top:' + top + '%;">' + msg + '</h1>');
						// 		top = top + 15;
						// 		delay = delay + animationGap;
						// 	}
						// 	else
						// 	{
						// 		jQuery('.licenseNotification').remove();
						// 		top = 25;
						// 		delay = 0;
						// 		animationDuration = 10;
						// 		animationGap = 1;
						// 	}
						// },1000);
				
						
						// var e = jQuery('#licenseNotification');
						// e.off('click');
						// var ele = document.querySelector('#licenseNotification');
						// ele.off('click');
// content.off('click');
					// }
					// alert("YOHO");
				}
				else
				{
					notificationEnabled = false;
					// jQuery(".licenseNotification").hide();					
				}
				// response.deviceData.forEach(device => {
				// 	let device_id = device.id;
				// 	if (device.dio_type == 999 && device.info.includes("C$"))
				// 	{
				// 		device_id = parseInt(device.info.replace("C$",""));
				// 	}
				// 	var deviceSpanNodes = jQuery("#txt" + device_id);
				// 	if (deviceSpanNodes.length > 0){
				// 		var deviceSpanNode = deviceSpanNodes.eq(0);
						
				// 	}
				// });
				
				setTimeout(getAuthenticationStatus, refresh_license_timeout);
			},
			error: function(response) {
				console.log("getAuthenticationStatus Error");
				setTimeout(getAuthenticationStatus, refresh_license_timeout);

			}
		});
	}
		function getRealtimeDeviceData()
		{
			// return;
			alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getRealtimeDeviceData').'?floor_id='.$myid; ?>";	
			
			jQuery.ajax({
				url: alprUrl,
				headers: {
					"Content-Type": "application/json"
				},
				type: "POST",
				data: "",
				success: function(response) {
					// console.log('realtime');
					var response = JSON.parse(response);
					// console.log(response);
					response.coSensorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("" + device.co + "ppm");
						}
					});
					response.pmSensorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							if (device.dio_type == 41)
							{
								deviceSpanNode.html("PM2.5: " + device.pm25 + " ug/m3");
								deviceSpanNode.html(deviceSpanNode.html() + "<br/>" + "PM10: " + device.pm10 + " ug/m3");
							}
						}
					});
					response.temperatureSensorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);							
							deviceSpanNode.html("溫度：" + device.temp + " 度");
							if (device.dio_type == 42)
							{
								deviceSpanNode.html(deviceSpanNode.html() + "<br/>照度：" + parseInt(device.lux) + " lux");
							}
							if (device.dio_type !=30 && device.dio_type !=42)
							{
								deviceSpanNode.html(deviceSpanNode.html() + "<br/>濕度：" + device.humidity + " %");
							}
							if (device.dio_type == 18 || device.dio_type == 30 ||  device.dio_type == 39)
							{
								deviceSpanNode.html(deviceSpanNode.html() + "<br/>" + "CO2:" + device.co2_ppm + " ppm");
							}
						}
					});
					// response.electroniMeterData.forEach(device => {
					// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
					// 	if (deviceSpanNodes.length > 0){
					// 		var deviceSpanNode = deviceSpanNodes.eq(0).siblin;
					// 		deviceSpanNode.html("累計用電：" + device.accumulateWaterFlow + "度");
					// 	}
					// });
					response.solarMeterData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("即時發電：" + device.elec_kw + "&nbsp;KW<br/>累計發電：" + device.elec_kwh + "&nbsp;KWH");
						}
					});
					response.waterMeterData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("水量積算：" + device.accumulateWaterFlow + "度");
						}
					});
					response.analogData.forEach(device => {
						if (device.dio_type == 22)
						{
							var deviceSpanNodes = jQuery("#txt_span_" + device.id);
							if (deviceSpanNodes.length > 0){
								var deviceSpanNode = deviceSpanNodes.eq(0);
								deviceSpanNode.html("" + device.decimal_value + "");
							}
						} 
						//通用AI
						else if (device.dio_type == 35) 
						{
							var deviceSpanNodes = jQuery("#txt_span_" + device.id);
							if (deviceSpanNodes.length > 0){
								var deviceSpanNode = deviceSpanNodes.eq(0);
								var text = '';
								if (device.ga_display_prefix.length > 0)
								{
									text = text + device.ga_display_prefix + ": ";
								}
								if (device.ga_is_lookup_table == "1")
								{
									text = text + device.text_value;
								}
								else
								{
									text = text + device.decimal_value;
								}
								if (device.ga_unit.length > 0)
								{
									text = text + " " + device.ga_unit;
								}
								deviceSpanNode.html("" + text + "");
							}
						}

				
					});
					response.liuchuanElevatorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("樓層：" + device.liuchuan_floor_display_name + "<br/>");
						}
					});
					response.mitsubishiElevatorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("樓層：" + device.mitsubishi_floor_display_name + "<br/>");
						}
					});
					response.fujiElevatorData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("樓層：" + device.floor + "<br/>");
						}
					});
					response.irtiIvaPersonDetectionData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("人數：" + device.decimal_value + "<br/>");
						}
					});
					response.irtiIvaPersonCountingData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("人數：" + device.decimal_value + "<br/>");
						}
					});
					response.smartLampData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("" + device.voltage + " V<br/>" + device.current + " A<br/>" + device.power +  " W<br/>" + device.accumulatePower + " W/H");
						}
					});
					response.jetecWindDirectionData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("" + device.text_value);
						}
					});
					response.weemaIaqData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("溫度：" + device.temperature + "<br/>濕度：" + device.humidity);
						}
					});
					response.jetecRainMeterData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("數值：" + device.text_value);
						}
					});
					response.jetecSoilMeterData.forEach(device => {
						var deviceSpanNodes = jQuery("#txt_span_" + device.id);
						if (deviceSpanNodes.length > 0){
							var deviceSpanNode = deviceSpanNodes.eq(0);
							deviceSpanNode.html("濕度：" + device.text_value + "％");
						}
					});
					// response.electronicMeterData.forEach(device => {
					// 	var deviceSpanNodes = jQuery("#txt_span_" + device.id);
					// 	if (deviceSpanNodes.length > 0){
					// 		var deviceSpanNode = deviceSpanNodes.eq(0);
					// 		deviceSpanNode.html("<br/>累計用電：" + device.elec_kwh + "度");
					// 	}
					// });
					setTimeout(getRealtimeDeviceData, refresh_realtime_data_timeout);
				},
				error: function(response) {
					console.log("getRealtimeDeviceData Error");
					setTimeout(getRealtimeDeviceData, refresh_realtime_data_timeout);

				}
			});
		}
		function getLogStatus()
    {
      //console.log("getLogStatus");
      //return;
      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getLogStatus'); ?>";

      var logvar = [];
      jQuery.each(m_logObj, function(index, value1) {
           if(value1.status == 1 || value1.status == 2 || value1.status == 101)
               logvar.push({id:value1.id});

      });

      var winvar = [];
			jQuery.each(m_winid, function(index, value1) {
           if(value1.mydev != 0 && value1.id && value1.id.closed)
					 {
						   console.log('win close');
               winvar.push({id:value1.mydev});
							 value1.mydev = 0;
						}

      });


      var myvar = {id:logvar,dev:winvar};

      //myvar = {};
      var jsonArray = JSON.stringify(myvar);

      //console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {

              //console.log(response);
              var obj = JSON.parse(response);
              //console.log(obj);
			  <?php if (!$suppress_auto_redirect): ?>
				if(true || is_alarm != obj.is_need_alarm)
				{

					is_alarm = obj.is_need_alarm;

					var myalarm = jQuery("#myalarm");
					var myalarm_source = myalarm.children('source').eq(0);
					if(is_alarm == 1)
					{
						var path = '/images/alarm_audios/' + obj.alarm_sound_file;
						// console.log(myalarm_source.attr('src'), path);
						if (myalarm_source.attr('src') != path)
						{
							// console.log('src' , myalarm_source.attr('src'));
							// console.log('path', path);
							// console.log('change path');
							myalarm_source.attr('src', path);
							myalarm[0].pause();
							myalarm[0].currentTime = 0;  // 修正：current_time -> currentTime

							// 修正：在 load() 之前註冊事件監聽器，避免時序問題
							myalarm[0].addEventListener('canplaythrough', function() {
								myalarm[0].play();
							}, { once: true });

							myalarm[0].load();
							// console.log(myalarm_source.attr('src'));
							// playFunc();
						}
						else
						{
							if (is_alarm != obj.is_need_alarm)
							{								
								myalarm[0].play();
							}
							// console.log('no change path');
						}

					}
					else
					{
						myalarm[0].pause();
						myalarm[0].currentTime = 0;  // 修正：current_time -> currentTime
					}
				}
              if(obj.max > m_max)
              {
								  console.log('getLogStatus '+m_max);
									console.log(obj);

    //   form.submit();
									do_device_state(obj);
									m_max = obj.max;  // 修正：更新 m_max 避免重複觸發

                  //alarm_funcx(obj);
              }
              else
              {
                  updateLog(obj.items);
                  setTimeout(getLogStatus, timeout_log);
              }
			  <?php endif; ?>
          },
          error: function(response) {
              console.log("error7");
              setTimeout(getLogStatus, timeout_log*10);  // 修正：呼叫正確的函數

          }
      });

    }

    function updateLog(arr) {

      jQuery.each(arr, function(index, value) {

          if(value.status == 3 || value.status == 0)
          {
              doUpdateLog(value);
          }

      });

    }

		function updatePicture(obj,path) {
			obj.image.src = "/"+path;
			console.log(path);
			console.log(obj.image.src);

			obj.valid = 1;

      addClass(obj,"myshow");
			removeClass(obj,"myhidden");

    }

		function addClass(obj,name) {
			var dm_div = document.getElementById("txt"+obj.id.toString());

		   arr = dm_div.className.split(" ");
		   if (arr.indexOf(name) == -1) {
		     dm_div.className += " " + name;
		   }
		}

		function removeClass(obj,name) {
			var element = document.getElementById("txt"+obj.id.toString());

		  element.className = element.className.replace("\b"+name+"\b/g", "");
		}

    function disableCoins(obj)
		{
			obj.valid = 0;

			addClass(obj,"myhidden");
			removeClass(obj,"myshow");

		}
    function updateStatus(obj) {

      path = null;
			if(obj.status == 1)
			{
				path = obj.green;
			}
			else if(obj.status == 2)
			{
				path = obj.yellow;
			}
			else
			{
				path = obj.red;
			}

			for (i = 0; i < coins.length; i += 1) {
				if(coins[i].id == obj.id)
				{
					if(coins[i].valid == 0)
					{
							if(path != null)
							{
								updatePicture(coins[i],path);
							}
					}
					else
					{
							if(path != null)
									updatePicture(coins[i],path);
							else
									disableCoins(coins[i]);
					}
					break;
				}
			}


    }

    function changeStatus(arr) {

      jQuery.each(arr, function(index, value) {

       var dm_div = document.getElementById("txt"+index.toString());
       if(dm_div != null)
       {
         fLen = m_myObj.length;

         for (i = 0; i < fLen; i++) {
             if(m_myObj[i].id == index)
             {
							 //console.log(m_myObj[i].status +' '+ value);
               if(m_myObj[i].status != value)
               {
								    if(value != 3)
										{
											funcx();
											return;
										}
                    m_myObj[i].status = value;

										updateStatus(m_myObj[i]);

										if(m_myObj[i].status != 2)
										    getDeviceFunc(index);

               }
             }
         }

       }

      });


    }



		function showPhoneCCTV(arr) {

      fLen = m_phoneCCTVObj.length;

			for (i = 0; i < fLen; i++) {

					m_phoneCCTVObj[i].open = 0;

			}

      jQuery.each(arr, function(index, value2) {
				 //console.log(index);
				 //console.log(value.id);

         found = false;
         fLen = m_phoneCCTVObj.length;

         for (i = 0; i < fLen; i++) {
             if(m_phoneCCTVObj[i].id == value2.id)
             {
                 found = true;
								 m_phoneCCTVObj[i].open = value2.open;
								 break;
             }

         }

      });

			fLen = m_phoneCCTVObj.length;

			for (i = 0; i < fLen; i++) {

					if(m_phoneCCTVObj[i].open == 0)
					{
						  console.log("close ");
						 if(m_phoneCCTVObj[i].winid)
						 {
                 console.log("winid");

                 m_phoneCCTVObj[i].winid.close();
								 //console.log("close cctv "+m_phoneCCTVObj[i].path);
                 funcx();
						 }
						 m_phoneCCTVObj.splice(i, 1);
						 break;
					}

			}


    }
    function alarm_funcx(obj)
       {
           // your code here
					 var build = '<?php echo $myfilter_building;?>';
					 first_char=build.charAt(0);

           is_not_include = false;

					 if(first_char == "D")
					 {
						 is_not_include = true;
						 build = build.replace("D", "");
					 }

          var res = build.split("-");
					is_find = false;
					jQuery.each(res, function(index, value) {
		           if(is_not_include == false && value.localeCompare(obj.building) == 0)
		           {
		               is_find = true;

									// break;
		           }
							 if(is_not_include != false && value.localeCompare(obj.building) != 0)
		           {
		               is_find = true;

									 //break;
		           }

		      });

          if((is_find == false) ||
             (obj.id != '<?php echo $myid;?>' )
				  )
					{
						  console.log("alarm_funcx");
						  console.log(obj);
					    alarmItemFunc(obj);
					}
					else
					    //alarmItemFunc(obj);
					    funcx();
       }

		function is_match_1(building,this_building)
    {
			  console.log("match "+building);
        var build = building.replace("filter_building=", "");

				first_char=build.charAt(0);

 			   is_not_include = false;

 			   if(first_char == "D")
 			   {
 				     is_not_include = true;
 				     build = build.replace("D", "");
 			   }

 			   var res = build.split("-");
 			   is_find = false;
				 is_not_find = true;
 			   jQuery.each(res, function(index, value) {
 					 if(is_not_include == false && value.localeCompare(this_building) == 0)
 					 {
 							 is_find = true;

 							 //break;
 					 }
 					 if(is_not_include != false && value.localeCompare(this_building) == 0)
 					 {
 							 is_not_find = false;

 							 //break;
 					 }

 			});

      ret = false;
      if(is_find != false || (is_not_include != false && is_not_find != false))
			{
				ret = true;
			}
      return ret;
    }
		function is_match(building,this_building)
		{
			  var res = building.split("&");
			  is_find = false;
			  jQuery.each(res, function(index, value) {

				 if(value.indexOf('filter_building=') == 0)
				 {
					   if(is_find == false)
						     is_find = is_match_1(value,this_building);

						 //break;
				 }


		    });

				return is_find;
		}
		function gotoItem(menu,obj) {

      form = document.getElementById('adminForm');

      form.group.value = obj.group;

			form.id.value = obj.floor;


			url = form.action.split("index.php");
      var res = menu.alias.split("-");
      if(res.length >= 2)
			{
			    form.action = url[0]+"index.php/"+res[0]+'/'+menu.alias;
			}
			else
			{
          form.action = url[0]+"index.php/"+menu.alias;
			}
      console.log(form.action);

      //return;
      if (typeof form.onsubmit == 'function') {
            form.onsubmit();
      }
      if (typeof form.fireEvent == "function") {
            form.fireEvent('onsubmit');
      }

      form.submit();

  	}

		function alarmItemFunc(obj)
		{
				 for(i=0;i<m_menuObj.length;i++)
         {
					   if(is_match(m_menuObj[i].link,obj.building) != false)
             {
							 console.log(m_menuObj[i].menu+"&user-group="+obj.group+"&user-id="+obj.floor);

               gotoItem(m_menuObj[i],obj);
							 //window.location.href = m_menuObj[i].menu+"&user-group="+obj.group+"&user-id="+obj.floor;

               break;
             }
         }

		}
    function funcx()
       {
       // your code here
       // break out here if needed

			 action = '<?php echo JRoute::_('index.php?option=com_floor&task=sroots&id='.$myid.'&group='.$mygroup.'&filter_building='.$myfilter_building, false, 2) ?>';
       window.location.href = action;
			 console.log('funcx '+action);
       }

    function set_icons() {

      var dm_div = document.getElementById("mydiv");

      var rect = dm_div.getBoundingClientRect();
      //console.log(rect.top, rect.right-rect.left, rect.bottom-rect.top, rect.left);

      var dm = null;
      var top = null;
      var left = null;
      <?php foreach ($devices as $key => $value): ?>
      dm = document.getElementById("txt<?php echo ($value->id);?>");

      //dm.setAttribute("style","display:block;width:30px");

      left = rect.left+<?php echo $value->pointx;?>;
      //dm.style.width='30px';
      dm.style.left = left + 'px';
      //dm.style.left = dm.style.left+rect.left;

      top = rect.top+<?php echo $value->pointy;?>;
      dm.style.top = top + 'px';

      //rect = dm.getBoundingClientRect();
      //console.log(rect.top, rect.right-rect.left, rect.bottom-rect.top, rect.left);
      //console.log(left,top);

      //console.log(<?php echo $value->pointx; ?>);
      //console.log(<?php echo $value->pointy; ?>);
      <?php endforeach;?>

  	}

    function setList(obj) {
       log = document.getElementById("mylog"+obj.id);
       if(obj.status == 0)
           log.style.backgroundColor = "white";
       else if(obj.status == 2)
           log.style.backgroundColor = "DodgerBlue";

       logtd = document.getElementById("mylogtd"+obj.id);
       logtd.innerHTML = obj.check_date+" "+obj.check_time;

       checknametd = document.getElementById("checknametd"+obj.id);
       checknametd.innerHTML = obj.check_name;

       document.getElementById("enable"+obj.id).checked = true;

    }

    function doUpdateLog(obj) {
			  console.log("doUpdateLog "+obj.status);
       log = document.getElementById("mylog"+obj.id);
       if(obj.status == 0 || obj.status == 3)
           log.style.backgroundColor = "white";
       else if(obj.status == 2)
           log.style.backgroundColor = "DodgerBlue";

       logtd = document.getElementById("mylogendtd"+obj.id);
       logtd.innerHTML = obj.end_date+" "+obj.end_time;


    }

    function objClick(index,info) {
        console.log(index);

				if (!confirm("<?php echo JText::_('COM_FLOOR_CHECK_MESSAGE'); ?>"+" "+info)) {
	        return false;
	      }
        for(i=0;i<m_myObj.length;i++)
        {
            //console.log(m_alarmObj[i][0].id);
            if(m_myObj[i].id == index)
            {

              DOAction(m_myObj[i]);
              break;
            }
        }

    }
		function doCctvShow(obj,index) {

			if(index == 0)
			{
				open = obj.open;
				path = obj.path;

				left = 0;
				top = 0;
			}
			else if(index == 1)
			{
				open = obj.open1;
				path = obj.path1;

				left = 600;
				top = 0;
			}
			else if(index == 2)
			{
				open = obj.open2;
				path = obj.path2;
				left = 0;
				top = 600;
			}
			else if(index == 3)
			{
				open = obj.open3;
				path = obj.path3;
				left = 600;
				top = 600;
			}

			console.log("cctvshow "+open);

			//return;
			if(open  == 1)
			{
				path = path;

				if(is_window_valid(path) == false)
				{
					jQuery.each(m_cctvTop, function(index1, value1) {
										if(value1.id == obj.group)
										{
											 path = value1.username+":"+value1.passwd+"@"+path;

												//break;
										}

					 });

						console.log("ready "+path);

						id = window.open("http://"+path, '', 'height=600,width=600,left='+mleft+',top='+mtop);
						var myhand = new Object();
						myhand.id = id;
						myhand.name = path;
						myhand.mydev = 0;
						m_winid.push(myhand);
				}
			}

		}
		function cctvShow(obj) {
      doCctvShow(obj,0);
			doCctvShow(obj,1);
			doCctvShow(obj,2);
			doCctvShow(obj,3);
    }
		function cctvClick(index) {
        console.log("cctvClick "+index);

        //return;
        cctv = 0;
        //frame = document.getElementById('myframe');

        for(i=0;i<m_myObj.length;i++)
        {
            //console.log(m_alarmObj[i][0].id);
            if(m_myObj[i].id == index)
            {
                path = m_myObj[i].name;
                cctv = m_myObj[i].cctv;

								if(is_window_valid(path) == false)
								{
									  //console.log(m_myObj[i].group);
										jQuery.each(m_cctvTop, function(index1, value1) {
									            if(value1.id == m_myObj[i].group)
									            {
									            //    path = value1.username+":"+value1.passwd+"@"+path;

																	//break;
									            }

									   });
					var fullUrlPath = m_myObj[i].web_view_scheme + "://" + path + ":" + m_myObj[i].web_view_port + m_myObj[i].web_view_subpath;
                    console.log("ready "+path);
									//    alert("clikkkkkkkk");
                     window.open(fullUrlPath, '', config='height=600,width=600');

					// 			    var myhand = new Object();
					// 			    myhand.id = id;
					// 			    myhand.name = fullUrlPath;
                    // myhand.mydev = 0;
					// 			    m_winid.push(myhand);

							  }
              break;
            }
        }

    }

    function phoneClick(index) {
        console.log("phoneClick "+index);

				if(index < phone_min || index > phone_max)
        {
					   alert("Error:號碼範圍在 "+phone_min+"和 "+phone_max+"區間");
				    return;
        }
				var number = prompt("請輸入電話號碼", "<?php echo($def_number);?>");
			  if (number != null) {
			    console.log(number);
					if(index == number)
					{
						alert("Error:號碼相同!");
						return;
					}
					doCallNumber(index,number);
			  }
        return;

    }
		function doCallNumber(caller,callee) {
      //console.log(index);

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.do_call'); ?>";
      myArray = {check_name:'<?php echo($userName);?>',caller:caller,callee:callee};

      var jsonArray = JSON.stringify(myArray);

      console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
              console.log(response);

              //var obj = JSON.parse(response);
              //setList(obj);

              //setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error8");
              //setTimeout(statusFunc, timeout*10);

          }
      });

    }

    function DOAction(object) {
      //console.log(index);

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.do_action'); ?>";
      myArray = {name:'<?php echo $userName;?>',id:object.id,dio_id:object.dio_id,index:object.index};

      var jsonArray = JSON.stringify(myArray);

      console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
              console.log(response);
              timeout = 1000;
              //var obj = JSON.parse(response);
              //setList(obj);

              //setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error2");
              //setTimeout(statusFunc, timeout*10);

          }
      });

    }

		function set_window_invalid(id) {

      jQuery.each(m_winid, function(index, value) {
          if(value.id == id)
					{
						value.id = null;

					}

      });

    }

		function is_window_valid(name) {

			var ret;

			ret = false;
      jQuery.each(m_winid, function(index, value) {
          if(value.name == name)
					{
						if(value.id && !value.id.closed)
						{
						    ret = true;
						    //break;
					  }

					}

      });

			return ret;

    }

    function closeCCTVWindow() {
      jQuery.each(m_winid, function(index, value) {
				  if(value.id)
              value.id.close();

      });

			jQuery.each(m_phoneCCTVObj, function(index, value) {
          if(value.winid)
					    value.winid.close();

      });



    }
		function getDevicelog(object,index) {
      console.log("getDevicelog "+index);

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getLog'); ?>";
      myArray = {id:index,name:"<?php echo $userName;?>"};

      var jsonArray = JSON.stringify(myArray);

      console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
              console.log('getDevicelog');
							console.log(response);

              var obj = JSON.parse(response);
              do_device_state(obj);
							//alarm_funcx(obj);

              //setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error13");
              //setTimeout(statusFunc, timeout*10);

          }
      });

    }
		function logClick(object,index) {
      console.log("logClick "+index);

			logtd = document.getElementById("mylogendtd"+index);
      console.log("123 "+logtd.innerHTML.length+" 456")
			if(logtd.innerHTML.length < 2)
			{
				getDevicelog(object,index);

      }
    }

    function mydbClick(object,index) {
      console.log(index);

      enable = document.getElementById('enable'+index);

      if(enable.checked)
      {
        return;
      }

      if (!confirm("<?php echo JText::_('COM_FLOOR_CHECK_MESSAGE'); ?>")) {
        return false;
      }

      alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.setCheck'); ?>";
      myArray = {id:index,name:"<?php echo $userName;?>"};

      var jsonArray = JSON.stringify(myArray);

      console.log(jsonArray);
      jQuery.ajax({
          url: alprUrl,
          headers: {
              "Content-Type": "application/json"
          },
          type: "POST",
          data: jsonArray,
          success: function(response) {
              console.log(response);

              var obj = JSON.parse(response);
              setList(obj);
							do_device_state(obj);
							//alarm_funcx(obj);

              //setTimeout(statusFunc, timeout);
          },
          error: function(response) {
              console.log("error3");
              //setTimeout(statusFunc, timeout*10);

          }
      });

    }


  function liclick(id)
	{
		var x = document.getElementById(id);

    if(x.style.display != "block")
		    x.style.display="block";
		else
		    x.style.display="none";

	}


	function playFunc()
	{
		   console.log(is_alarm);
		   if(is_alarm == 1)
			 {
	         document.getElementById("myalarm").play();

	         setTimeout(playFunc, alarm_timeout);
		   }

	 }

<?php endif;?>
  </script>
