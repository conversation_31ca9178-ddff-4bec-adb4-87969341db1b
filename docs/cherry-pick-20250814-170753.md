# Cherry-pick 修改記錄

**時間**: 2025-08-14 17:07:53

## 修改概述
將 Git commit `4b868f799f6671d5eba2c15f4c2cea2f406cc4e5` 的修改內容套用到 main 分支。

## 原始 Commit 資訊
- **Commit Hash**: 4b868f799f6671d5eba2c15f4c2cea2f406cc4e5
- **分支**: 1.0.67
- **作者**: <PERSON> <<EMAIL>>
- **日期**: Thu Aug 14 17:07:53 2025 +0800
- **訊息**: improve side menu badge algo

## 新 Commit 資訊
- **Commit Hash**: 5d9fed0
- **分支**: main
- **作者**: <PERSON> <<EMAIL>>
- **日期**: Thu Aug 14 17:07:53 2025 +0800
- **訊息**: improve side menu badge algo

## 修改的檔案
- `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`

## 修改內容說明
改善了側邊選單徽章演算法，主要變更包括：

1. **錯誤處理改善**: 
   - 在 AJAX 成功回調中加入 try-catch 錯誤處理
   - 增加詳細的錯誤記錄和診斷資訊

2. **演算法重構**:
   - 將原本的單一迴圈處理改為三個階段的處理流程
   - 第一輪：初始化 sum 值
   - 第二輪：計算子選單總和並顯示個別數字
   - 第三輪：顯示父選單總和

3. **程式碼清理**:
   - 移除了一些註解掉的 console.log 語句
   - 改善了程式碼結構和可讀性

## 執行步驟
1. 從 1.0.67 分支暫存未提交的變更
2. 切換到 main 分支
3. 使用 `git cherry-pick 4b868f799f6671d5eba2c15f4c2cea2f406cc4e5` 套用修改
4. 成功完成 cherry-pick，無衝突

## 統計資訊
- 1 個檔案變更
- 59 行新增
- 44 行刪除

## 注意事項
- Cherry-pick 過程中自動合併成功，無需手動解決衝突
- 原始分支 1.0.67 的暫存變更仍保留在 stash 中
