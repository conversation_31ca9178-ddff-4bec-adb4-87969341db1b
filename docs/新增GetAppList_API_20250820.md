# 新增 GetAppList API 記錄

**修改時間：** 2025年8月20日  
**分支：** 1.0.67  
**修改檔案：** `web/com_top-1.0.0/site/helpers/toputility.php`  
**修改人員：** Augment Agent  

## 修改概述

在 `toputility.php` 中新增了 `GetAppList()` API 方法，用於取得所有啟用的 APK 應用程式清單，並以指定的 JSON 格式輸出。

## 新增的方法

### GetAppList() - 主要 API 方法
**位置：** 第231-264行
**功能：** 直接在 SQL 中完成欄位對應和格式化，取得所有啟用的 APK 應用程式清單

```php
/**
 * 取得所有啟用的 APK 應用程式清單
 * 直接在 SQL 中完成欄位對應和格式化，只過濾 state=1 的記錄
 *
 * @return string JSON 格式的應用程式清單
 */
public static function GetAppList()
```

## SQL 查詢特點

**直接在 SELECT 中完成欄位對應：**
```sql
SELECT
    a.app AS App,
    CAST(a.note AS SIGNED) AS AppId,
    CAST(a.note1 AS SIGNED) AS Channel,
    a.app AS AppName,
    a.name AS PackageName,
    a.version AS VersionName,
    a.path AS FilePath
FROM #__apkupdate_table AS a
WHERE a.state = 1
```

## 資料庫欄位對應

根據使用者需求，欄位對應關係如下：

| 輸出欄位 | 資料庫欄位 | 資料類型轉換 | 說明 |
|---------|-----------|-------------|------|
| App | app | 字串 | 應用程式名稱 |
| AppId | note | int | 應用程式 ID (轉為整數) |
| Channel | note1 | int | 頻道編號 (轉為整數) |
| AppName | app | 字串 | 應用程式名稱 (與 App 相同) |
| PackageName | name | 字串 | 套件名稱 |
| VersionName | version | 字串 | 版本名稱 |
| FilePath | path | 字串 | 檔案路徑 |

## 輸出格式範例

API 會輸出以下格式的 JSON 陣列：

```json
[
    {
        "App": "App1-1",
        "AppId": 1,
        "Channel": 1,
        "AppName": "softphone1",
        "PackageName": "weema.Android.sip",
        "VersionName": "2025-08-20",
        "FilePath": "/images/softphone1-debug-2025-08-20.apk"
    }
]
```

## 過濾條件

- **狀態過濾：** 只取得 `state = 1` 的記錄 (啟用狀態)
- **資料來源：** `#__apkupdate_table` 資料表
- **查詢方式：** 使用 `DISTINCT` 避免重複記錄

## 實作特點

1. **直接 SQL 處理：**
   - 在 SQL SELECT 中直接完成欄位重新命名和對應
   - 使用 `CAST(a.note AS SIGNED)` 和 `CAST(a.note1 AS SIGNED)` 將字串轉為整數
   - 避免 PHP 層面的額外處理，提升效能

2. **簡化程式邏輯：**
   - 單一方法完成所有功能，無需額外的輔助方法
   - 直接回傳 JSON 格式，減少記憶體使用

3. **資料庫最佳化：**
   - 只查詢需要的欄位，避免 `SELECT *`
   - 在資料庫層面完成資料轉換，減少網路傳輸量

## 使用方式

可以透過以下方式呼叫此 API：

```php
$appList = TopUtility::GetAppList();
echo $appList; // 輸出 JSON 格式的應用程式清單
```

## 注意事項

1. **資料庫依賴：** 需要確保 `#__apkupdate_table` 資料表存在且包含必要欄位
2. **權限控制：** 此方法為 public static，可直接呼叫，如需權限控制請另外實作
3. **效能考量：** 查詢所有啟用記錄，如資料量大可考慮加入分頁機制
4. **資料驗證：** 目前未對 `note` 和 `note1` 欄位進行數值驗證，如需要可加入檢查機制
