# 新增 GetAppList API 記錄

**修改時間：** 2025年8月20日  
**分支：** 1.0.67  
**修改檔案：** `web/com_top-1.0.0/site/helpers/toputility.php`  
**修改人員：** Augment Agent  

## 修改概述

在 `toputility.php` 中新增了 `GetAppList()` API 方法，用於取得所有啟用的 APK 應用程式清單，並以指定的 JSON 格式輸出。

## 新增的方法

### 1. GetAppList() - 主要 API 方法
**位置：** 第223-250行
**功能：** 取得所有啟用的 APK 應用程式清單並轉換為指定格式

```php
/**
 * 取得所有啟用的 APK 應用程式清單
 * 只過濾 state=1 的記錄，並轉換為指定的 JSON 格式
 * 
 * @return string JSON 格式的應用程式清單
 */
public static function GetAppList()
```

### 2. findAllActiveApks() - 資料查詢方法
**位置：** 第252-281行
**功能：** 查詢所有啟用狀態的 APK 記錄

```php
/**
 * 查詢所有啟用狀態的 APK 記錄
 * 只取得 state=1 的記錄
 * 
 * @return array 資料庫查詢結果陣列
 */
private static function findAllActiveApks()
```

## 資料庫欄位對應

根據使用者需求，欄位對應關係如下：

| 輸出欄位 | 資料庫欄位 | 資料類型轉換 | 說明 |
|---------|-----------|-------------|------|
| App | app | 字串 | 應用程式名稱 |
| AppId | note | int | 應用程式 ID (轉為整數) |
| Channel | note1 | int | 頻道編號 (轉為整數) |
| AppName | app | 字串 | 應用程式名稱 (與 App 相同) |
| PackageName | name | 字串 | 套件名稱 |
| VersionName | version | 字串 | 版本名稱 |
| FilePath | path | 字串 | 檔案路徑 |

## 輸出格式範例

API 會輸出以下格式的 JSON 陣列：

```json
[
    {
        "App": "App1-1",
        "AppId": 1,
        "Channel": 1,
        "AppName": "softphone1",
        "PackageName": "weema.Android.sip",
        "VersionName": "2025-08-20",
        "FilePath": "/images/softphone1-debug-2025-08-20.apk"
    }
]
```

## 過濾條件

- **狀態過濾：** 只取得 `state = 1` 的記錄 (啟用狀態)
- **資料來源：** `#__apkupdate_table` 資料表
- **查詢方式：** 使用 `DISTINCT` 避免重複記錄

## 實作特點

1. **資料類型轉換：** 
   - `note` 和 `note1` 欄位使用 `(int)` 強制轉換為整數
   - 其他欄位保持原始字串格式

2. **錯誤處理：**
   - 使用 Joomla 標準的資料庫查詢方式
   - 回傳空陣列當沒有符合條件的記錄時

3. **程式碼結構：**
   - 主要方法 `GetAppList()` 負責格式轉換和輸出
   - 輔助方法 `findAllActiveApks()` 負責資料庫查詢
   - 遵循現有程式碼的命名和結構慣例

## 使用方式

可以透過以下方式呼叫此 API：

```php
$appList = TopUtility::GetAppList();
echo $appList; // 輸出 JSON 格式的應用程式清單
```

## 注意事項

1. **資料庫依賴：** 需要確保 `#__apkupdate_table` 資料表存在且包含必要欄位
2. **權限控制：** 此方法為 public static，可直接呼叫，如需權限控制請另外實作
3. **效能考量：** 查詢所有啟用記錄，如資料量大可考慮加入分頁機制
4. **資料驗證：** 目前未對 `note` 和 `note1` 欄位進行數值驗證，如需要可加入檢查機制
