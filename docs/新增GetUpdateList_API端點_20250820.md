# 新增 GetUpdateList API 端點記錄

**修改時間：** 2025年8月20日  
**分支：** 1.0.67  
**修改人員：** Augment Agent  

## 修改概述

為 `GetUpdateList()` 方法新增 API 端點，讓它可以透過 HTTP 請求被呼叫，類似現有的 `MyUpdate` API。

## 新增的檔案

### 1. API 資源檔案
**檔案：** `web/com_api-master/code/plugins/api/users/users/getupdatelist.php`

```php
<?php
/**
 * API 資源：取得更新清單
 * 呼叫 TopUtility::GetUpdateList() 方法取得所有啟用的 APK 應用程式清單
 */
class UsersApiResourceGetupdatelist extends ApiResource
{
    /**
     * GET 請求處理
     * 取得所有啟用的 APK 應用程式清單
     */
    public function get()
    {
        $result = new stdClass;
        
        // 呼叫 TopUtility::GetUpdateList() 方法
        $updateList = TopHelpersUtility::GetUpdateList();

        $result->json = $updateList;
        
        $this->plugin->setResponse( $result );
    }

    /**
     * POST 請求處理
     * 與 GET 請求相同的處理邏輯
     */
    public function post()
    {
        // POST 請求使用與 GET 相同的邏輯
        return $this->get();
    }
}
```

## 修改的檔案

### 1. API 插件註冊
**檔案：** `web/com_api-master/code/plugins/api/users/users.php`

在 `__construct` 方法中新增：
```php
$this->setResourceAccess('getupdatelist', 'public', 'post');
$this->setResourceAccess('getupdatelist', 'public', 'get');
```

## API 使用方式

### HTTP GET 請求
```
GET /index.php?option=com_api&app=users&resource=getupdatelist&format=raw
```

### HTTP POST 請求
```
POST /index.php?option=com_api&app=users&resource=getupdatelist&format=raw
```

### cURL 範例
```bash
# GET 請求
curl -X GET "https://your-domain.com/index.php?option=com_api&app=users&resource=getupdatelist&format=raw"

# POST 請求
curl -X POST "https://your-domain.com/index.php?option=com_api&app=users&resource=getupdatelist&format=raw"
```

## API 回應格式

### 成功回應
```json
{
    "json": "[
        {
            \"app\": \"App1-1\",
            \"appId\": 1,
            \"channel\": 1,
            \"appName\": \"softphone1\",
            \"packageName\": \"weema.Android.sip\",
            \"versionName\": \"2025-08-20\",
            \"filePath\": \"/images/softphone1-debug-2025-08-20.apk\"
        }
    ]"
}
```

### 解析後的資料結構
```json
[
    {
        "app": "App1-1",
        "appId": 1,
        "channel": 1,
        "appName": "softphone1",
        "packageName": "weema.Android.sip",
        "versionName": "2025-08-20",
        "filePath": "/images/softphone1-debug-2025-08-20.apk"
    }
]
```

## 與現有 API 的比較

### MyUpdate API
- **端點：** `/index.php?option=com_api&app=users&resource=update&format=raw`
- **功能：** 取得特定應用程式的版本資訊
- **參數：** 需要 `updatename` 參數
- **回應：** 單一應用程式的版本和路徑資訊

### GetUpdateList API
- **端點：** `/index.php?option=com_api&app=users&resource=getupdatelist&format=raw`
- **功能：** 取得所有啟用應用程式的完整清單
- **參數：** 無需參數
- **回應：** 所有啟用應用程式的詳細資訊陣列

## 安全性設定

- **存取權限：** 設定為 `public`，無需認證即可存取
- **支援方法：** 同時支援 GET 和 POST 請求
- **資料過濾：** 只回傳 `state=1` 的啟用記錄

## 實作特點

1. **一致性設計：** 遵循現有 API 的設計模式和命名慣例
2. **錯誤處理：** 繼承 `ApiResource` 的錯誤處理機制
3. **效能優化：** 直接在 SQL 層面完成資料轉換，減少 PHP 處理
4. **格式統一：** 回應格式與其他 API 保持一致

## 測試建議

1. **基本功能測試：**
   - 測試 GET 和 POST 請求是否正常回應
   - 驗證回應的 JSON 格式是否正確

2. **資料驗證：**
   - 確認只回傳 `state=1` 的記錄
   - 驗證欄位對應是否正確（appId, channel 等）

3. **效能測試：**
   - 測試大量資料時的回應時間
   - 驗證記憶體使用情況

4. **整合測試：**
   - 與現有 API 系統的相容性
   - 確認不影響其他 API 端點的運作

## 注意事項

1. **API 版本：** 基於現有的 com_api 框架實作
2. **快取機制：** 目前未實作快取，如需要可後續新增
3. **分頁支援：** 目前回傳所有記錄，如資料量大可考慮加入分頁
4. **認證機制：** 目前為公開 API，如需要可後續加入認證
