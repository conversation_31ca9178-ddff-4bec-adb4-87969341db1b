#ifndef __TEMPNODEYONGJIA3IN1CO_HPP__
#define __TEMPNODEYONGJIA3IN1CO_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class tempNodeYonGjia3In1Co : public analogNode
{
public:
    tempNodeYonGjia3In1Co();
    virtual ~tempNodeYonGjia3In1Co();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void updateMessage();
  void sendMessageToBa();
  float humidity_weight;
// private:
  int m_id;
  int m_dio_type;
  int m_humidity_alarmdir;
  int m_humidity_alarm;
  int m_co_ppm_alarm;
  int m_co_ppm_alarmdir;
  int m_temperature_alarmdir;
  int m_temperature_alarm;
  int m_co_ppm_alarm_threshold;
  float m_humidity_alarm_threshold;
  float m_temperature_alarm_threshold;
  bool m_co_ppm_alarm_triggered = false;
  bool m_humidity_alarm_triggered = false;
  bool m_temperature_alarm_triggered = false;
  
  int status = 1;
  int last_update_timestamp;
  float m_temp;
  float m_humidity;
  int m_co_ppm;

  uint8_t m_buf[10];
};

#endif
