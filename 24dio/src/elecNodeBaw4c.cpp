#include <ctime>
#include "elecNodeBaw4c.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10

elecNodeBaw4c::elecNodeBaw4c()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  V_BN = V_CN = A_A = A_B = A_C = kvar = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeBaw4c::~elecNodeBaw4c()
{
}

int elecNodeBaw4c::set(Json::Value it)
{
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &m_dio_type, "dio_type");

  // BAW-4C 電表主要測量項目位址 (只讀取必要的數值)
  int reg_arr[] =
      {
          0xA000,  // KWH 有功總電能 2 registers 0.01kwh
          0x141,   // Psum 系統有效功率 1 register 0.01kw
          0x131,   // V1 相電壓 2 registers 0.01v
          0x130,   // F 頻率 1 register 0.01hz
          0x14D,   // PF 系統功率因數 1 register 0.001
          0x133,   // V2 相電壓 2 registers 0.01v
          0x135,   // V3 相電壓 2 registers 0.01v
          0x138,   // I1 相電流 2 registers 0.01a
          0x13A,   // I2 相電流 2 registers 0.01a
          0x13C,   // I3 相電流 2 registers 0.01a
          0x145    // Qsum 系統無效功率 1 register 0.01kvar
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    if (i == 0 || i == 2 || i == 5 || i == 6 || i == 7 || i == 8 || i == 9) // 2 registers for KWH, V1, V2, V3, I1, I2, I3
      send[5] = 0x02;
    else
      send[5] = 0x01; // 1 register for others

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();
  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeBaw4c::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }

  // 檢查功能碼和錯誤回應
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }
    cout << "BAW-4C response 83 error" << endl;
    return true;
  }

  // 計算實際資料長度並驗證CRC
  int data_length = p_data[2];  // 第3個位元組是資料長度
  int aspect_length = 3 + data_length;  // 地址(1) + 功能碼(1) + 長度(1) + 資料(data_length)
  uint16_t crc = crc_chk(p_data, aspect_length);

  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }
    cout << "BAW-4C crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    return true;
  }

  unsigned int int_val;
  uint8_t buf4[4];

  switch (m_index)
  {
  case 0: // KWH 有功總電能 2 registers 0.01kwh
    // 根據BAW-4C規格：低位址是高位元組資料
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    kwh = (long)int_val; // 已經是 0.01kWh 單位，直接使用
    break;
  case 1: // Psum 系統有效功率 1 register 0.01kw
    int_val = (p_data[3] << 8) | p_data[4];
    kw = (int)int_val; // 已經是 0.01kW 單位，直接使用
    break;
  case 2: // V1 相電壓 2 registers 0.01v
    // 根據BAW-4C規格：低位址是高位元組資料
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    volage = (int)int_val; // 已經是 0.01V 單位，直接使用
    break;
  case 3: // F 頻率 1 register 0.01hz
    int_val = (p_data[3] << 8) | p_data[4];
    freq = (int)int_val; // 已經是 0.01Hz 單位，直接使用
    break;
  case 4: // PF 系統功率因數 1 register 0.001
    int_val = (p_data[3] << 8) | p_data[4];
    power_factor = (int)int_val / 10; // 轉換為內部格式 (0.001 -> 0.01)
    break;
  case 5: // V2 相電壓 2 registers 0.01v
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    V_BN = (int)int_val; // 已經是 0.01V 單位，直接使用
    break;
  case 6: // V3 相電壓 2 registers 0.01v
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    V_CN = (int)int_val; // 已經是 0.01V 單位，直接使用
    break;
  case 7: // I1 相電流 2 registers 0.01a
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    current = A_A = (int)int_val; // 已經是 0.01A 單位，直接使用
    break;
  case 8: // I2 相電流 2 registers 0.01a
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    A_B = (int)int_val; // 已經是 0.01A 單位，直接使用
    break;
  case 9: // I3 相電流 2 registers 0.01a
    int_val = (p_data[3] << 24) | (p_data[4] << 16) | (p_data[5] << 8) | p_data[6];
    A_C = (int)int_val; // 已經是 0.01A 單位，直接使用
    break;
  case 10: // Qsum 系統無效功率 1 register 0.01kvar
    int_val = (p_data[3] << 8) | p_data[4];
    kvar = (int)int_val; // 已經是 0.01kVar 單位，直接使用
    updateMessage(); // 最後一個參數處理完後更新訊息
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}

void elecNodeBaw4c::setKWH(long kwh)
{
    this->kwh = kwh;
}

long elecNodeBaw4c::getKWH()
{
    return kwh;
}
