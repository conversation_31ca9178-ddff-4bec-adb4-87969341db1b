#include <ctime>
#include "elecNodeVmrMp8.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeVmrMp8::elecNodeVmrMp8()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  // timestamp = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeVmrMp8::~elecNodeVmrMp8()
{
}

int elecNodeVmrMp8::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
         0,   // Vab
         2,   // Vbc
         4,   // Vca
         6,   // Ia
         8,   // Ib
         10,  // Ic
         52,  // Kw
         60,  // Kvar
         70,  // Freq
         114, // Kwh
         64,  // Pf
         48,  // I
      };
      
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    send[5] = 2;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeVmrMp8::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  int aspect_length =7;  
  // if (m_index == 0)
  // {
  //   aspect_length = 43;
  // }
  // else if (m_index == 1)
  // {
  //   aspect_length = 15;
  // }
  // else if (m_index == 2)
  // {
  //   aspect_length = 11;
  // }
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec vmr mp8 response 83 error" << endl;
    m_index = 0;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec vmr mp8 crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    m_index = 0;
    return true;
  }
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  int int_value;
  uint8_t buf[4];

  switch (m_index)
  {
  case 0:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    volage = (*(uint16_t *)&buf[0]) *100;
    break;
  case 1:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    V_BN = (*(uint16_t *)&buf[0]) *100;
    break;
  case 2:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    V_CN = (*(uint16_t *)&buf[0]) *100;
    break;
  case 3:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_A = (*(uint16_t *)&buf[0]) *100;
    break;
  case 4:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_B = (*(uint16_t *)&buf[0]) *100;
    break;
  case 5:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_C = (*(uint16_t *)&buf[0]) *100;
    break;
  case 6:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    kw = ((*(uint16_t *)&buf[0]) *100 )/1000;
    // kwh = (int)int_value;
    break;
  case 7:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    kvar = (*(uint16_t *)&buf[0]) *100;
    break;
  case 8:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    freq = (*(uint16_t *)&buf[0]) *100;
    break;
  case 9:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    kwh = (*(uint16_t *)&buf[0]) *100;
    break;
  case 10:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    power_factor = (*(uint16_t *)&buf[0]) *100;
    updateMessage();
   
    // buf[0] = p_data[22];
    // buf[1] = p_data[21];
    // buf[2] = p_data[20];
    // buf[3] = p_data[19];
    // current = (*(uint16_t *)&buf[0]) *100;
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}