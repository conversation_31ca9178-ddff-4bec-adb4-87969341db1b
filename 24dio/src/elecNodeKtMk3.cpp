#include <ctime>
#include "elecNodeKtMk3.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10

/**
 * KT-MK3 電表節點建構子
 * 初始化所有測量值為 0
 */
elecNodeKtMk3::elecNodeKtMk3()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

/**
 * KT-MK3 電表節點解構子
 */
elecNodeKtMk3::~elecNodeKtMk3()
{
}

/**
 * 設定 KT-MK3 電表參數並建立 Modbus 指令
 * 根據 KT-MK3 電表規格建立 12 個讀取指令
 * 使用功能碼 0x04 (讀取 Input Register)，每個測量值讀取 2 個暫存器 (32bit float)
 */
int elecNodeKtMk3::set(Json::Value it)
{
  // 呼叫父類別的設定方法
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &m_dio_type, "dio_type");

  // KT-MK3 電表測量項目位址定義
  // 每個項目使用 2 個暫存器 (32bit sw. float)
  int reg_arr[] = {
      7,   // Vab 線電壓 AB (7~8)
      9,   // Vbc 線電壓 BC (9~10)
      11,  // Vca 線電壓 CA (11~12)
      13,  // Ia A相電流 (13~14)
      15,  // Ib B相電流 (15~16)
      17,  // Ic C相電流 (17~18)
      67,  // I 總電流 (67~68)
      41,  // Kvar 無功功率 (41~42) *0.001
      49,  // pf 功率因數 (49~50)
      51,  // freq 頻率 (51~52)
      79,  // kwh 累積電能 (79~80) *0.001
      25   // kw 有功功率 (25~26) *0.001
  };

  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  // 為每個測量項目建立 Modbus 讀取指令
  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;           // 設備地址
    send[1] = 0x04;             // 功能碼：讀取 Input Register
    send[2] = reg_arr[i] / 256; // 起始位址高位元組
    send[3] = reg_arr[i] % 256; // 起始位址低位元組
    send[4] = 0;                // 讀取數量高位元組
    send[5] = 2;                // 讀取數量低位元組 (2個暫存器 = 32bit float)

    // 計算並附加 CRC 校驗碼
    crc = crc_chk(send, 6);
    send[6] = crc % 256;        // CRC 低位元組
    send[7] = crc / 256;        // CRC 高位元組

    // 建立 RS485 訊息並加入訊息佇列
    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  // 初始化迭代器
  iterator = m_msg.begin();

  // 將此電表節點加入到系統中
  baUtil::addElecNodes(this);

  return 1;
}

/**
 * 處理從 KT-MK3 電表接收到的資料
 * 解析 32bit float 格式的測量值並套用縮放係數
 */
bool elecNodeKtMk3::set_data(uint8_t *p_data, int len)
{
  // 檢查設備地址是否匹配
  if (p_data[0] != m_addr)
  {
    return false;
  }
  
  // 計算預期的回應長度：地址(1) + 功能碼(1) + 資料長度(1) + 資料(4) + CRC(2) = 9
  int aspect_length = 7;
  
  // 驗證 CRC 校驗碼
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x84 || p_data[1] != 0x04)  // 檢查功能碼錯誤
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }
    cout << "KT-MK3 response 84 error" << endl;
    m_index = 0;
    return true;
  }
  
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }
    cout << "KT-MK3 crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    m_index = 0;
    return true;
  }
  
  // 解析 32bit float 資料
  // KT-MK3 使用 sw. float 格式，需要進行位元組順序轉換
  float val;
  uint8_t buf[4];
  
  // 重新排列位元組順序以符合 32bit sw. float 格式
  buf[0] = p_data[6];  // 最低位元組
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];  // 最高位元組
  
  val = *(float *)&buf[0];
  
  // 根據測量項目索引處理不同的測量值
  switch (m_index)
  {
  case VAB_INDEX: // Vab 線電壓 AB
    volage = val * 100;  // 轉換為內部格式 (V * 100)
    break;
    
  case VBC_INDEX: // Vbc 線電壓 BC
    V_BN = val * 100;    // 轉換為內部格式 (V * 100)
    break;
    
  case VCA_INDEX: // Vca 線電壓 CA
    V_CN = val * 100;    // 轉換為內部格式 (V * 100)
    break;
    
  case IA_INDEX: // Ia A相電流
    A_A = val * 100;     // 轉換為內部格式 (A * 100)
    break;
    
  case IB_INDEX: // Ib B相電流
    A_B = val * 100;     // 轉換為內部格式 (A * 100)
    break;
    
  case IC_INDEX: // Ic C相電流
    A_C = val * 100;     // 轉換為內部格式 (A * 100)
    break;
    
  case I_INDEX: // I 總電流
    current = val * 100; // 轉換為內部格式 (A * 100)
    break;
    
  case KVAR_INDEX: // Kvar 無功功率 (*0.001)
    kvar = val * 0.001 * 100;  // 套用縮放係數並轉換為內部格式
    break;
    
  case PF_INDEX: // pf 功率因數
    power_factor = val * 100;  // 轉換為內部格式
    break;
    
  case FREQ_INDEX: // freq 頻率
    freq = val * 100;    // 轉換為內部格式 (Hz * 100)
    break;
    
  case KWH_INDEX: // kwh 累積電能 (*0.001)
    kwh = val * 0.001 * 100;   // 套用縮放係數並轉換為內部格式
    break;
    
  case KW_INDEX: // kw 有功功率 (*0.001)
    kw = val * 0.001 * 100;    // 套用縮放係數並轉換為內部格式
    updateMessage(); // 最後一個參數處理完後更新訊息
    break;
  }

  // 移動到下一個測量項目
  m_index++;

  // 如果所有測量項目都處理完畢，重置索引
  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}

/**
 * 設定累積電能值
 */
void elecNodeKtMk3::setKWH(long kwh)
{
    this->kwh = kwh;
}

/**
 * 取得累積電能值
 */
long elecNodeKtMk3::getKWH()
{
    return kwh;
}
