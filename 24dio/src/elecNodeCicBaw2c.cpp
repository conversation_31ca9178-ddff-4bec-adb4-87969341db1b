#include <ctime>
#include "elecNodeCicBaw2c.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeCicBaw2c::elecNodeCicBaw2c()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  // timestamp = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeCicBaw2c::~elecNodeCicBaw2c()
{
}


int elecNodeCicBaw2c::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  // int reg_arr[] =
  //     {
  //         6
  //     };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  // for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = 0x00;
    send[3] = 0x07;
    send[4] = 0;
    send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeCicBaw2c::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  int aspect_length = 7;  
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec elecNodeCicBaw2c response 83 error" << endl;
    m_index = 0;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec elecNodeCicBaw2c crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    m_index = 0;
    return true;
  }
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  // unsigned int float_value;
  uint8_t buf[4];

  buf[0] = p_data[6];
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;

  // float_value = (*(uint32_t *)&buf[0]) *100;
  switch (m_index)
  {
  case 0:
    kwh = (int)(*(uint32_t *)&buf[0]) *100;
    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}