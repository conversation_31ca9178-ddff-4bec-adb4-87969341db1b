#ifndef __WATERMETERNODECC130_HPP__
#define __WATERMETERNODECC130_HPP__
#include <json/json.h>

#include "analogNode.hpp"

/**
 * CC130 數位水表節點類別
 * 實作 CC130 水表透過 Modbus RTU over TCP 協議的數據讀取
 * 支援累計用量值和小數點位數的讀取，包含 CRC 校驗
 */
class waterMeterNodeCc130 : public analogNode
{
public:
  waterMeterNodeCc130();
  virtual ~waterMeterNodeCc130();

  /**
   * 設定節點參數並建立 Modbus 訊息
   * @param value JSON 設定值
   * @return 成功返回 1
   */
  int set(Json::Value value);
  
  /**
   * 處理接收到的 Modbus 數據
   * @param p_data 接收到的數據指標
   * @param len 數據長度
   * @return 處理成功返回 true
   */
  bool set_data(uint8_t *p_data, int len);
  
  /**
   * 取得節點索引
   * @return 節點索引值
   */
  int getIndex();
  
  /**
   * 取得程序 ID
   * @return 程序 ID
   */
  int getPid();

protected:
  /**
   * 更新訊息到系統
   * 定期將水表讀數更新到資料庫
   */
  void updateMessage();

protected:
  int index;                    // 節點索引
  int m_id;                     // 裝置 ID
  int m_dio_type;               // DIO 類型
  int update_timestamp;         // 上次更新時間戳
  double accumulateWaterFlow;   // 累計用水量
  int decimal_places;           // 小數點位數
};

#endif
