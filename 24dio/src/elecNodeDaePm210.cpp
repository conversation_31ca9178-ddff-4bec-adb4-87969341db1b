#include <ctime>
#include "elecNodeDaePm210.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeDaePm210::elecNodeDaePm210()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeDaePm210::~elecNodeDaePm210()
{
}

int elecNodeDaePm210::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          86,    // KWH 4 0.01kwh 
          38,    // psum 2 (kw total?) 0.001kw
          26,    // V1 (V A-B) 0.1v          
          44,    // F (HZ) 0.01hz
          46,    // PF 0.001
          28,    // V2 (V BN?) 0.1v
          30,    // V3 (V CN?) 0.1v
          32,    // I1 (A A?) 0.001a
          34,    // I2 (A B?) 0.001a
          36,    // I3 (A C?) 0.001a
          40     // qsum (kvar?) 0.001kvar
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = (reg_arr[i] +0)/ 256;
    send[3] = (reg_arr[i] +0)% 256;
    send[4] = 0;    
    send[5] = 0x02;


    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeDaePm210::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  int aspect_length = 4;

  aspect_length += 3;
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "dae pm210 response 83 error" << endl;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "dae pm210 crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    return true;
  }
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  // usleep(10000);
  //  float val;
  //  float *p_val;
  //  int* int_val_1;
  unsigned int int_val;
  uint8_t buf4[4];

  int reg_arr[] =
      {
          86,    // 0 KWH 4 0.01kwh 
          38,    // 1 psum 2 (kw total?) 0.001kw
          26,    // 2 V1 (V A-B) 0.1v          
          44,    // 3 F (HZ) 0.01hz
          46,    // 4 PF 0.001
          28,    // 5 V2 (V BN?) 0.1v
          30,    // 6 V3 (V CN?) 0.1v
          32,    // 7 I1 (A A?) 0.001a
          34,    // 8 I2 (A B?) 0.001a
          36,    // 9 I3 (A C?) 0.001a
          110     // 10 qsum (kvar?) 0.001kvar
      };
  int_val = *(int *)&buf4[0];
  switch (m_index)
  {
  case 0:
    // buf4[0] = p_data[6];
    // buf4[1] = p_data[5];
    // buf4[2] = p_data[4];
    // buf4[3] = p_data[3];
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    kwh = (long)*(uint32_t *)&buf4[0];
    // updateMessage();
    break;
  case 1:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    kw = (int)*(uint32_t *)&buf4[0] / 10;
    break;
  case 2:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    volage = (int)*(uint32_t *)&buf4[0]*10;
    break;

  case 3:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    freq = (int)*(uint16_t *)&buf4[0]*10;
    break;
  case 4:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    power_factor = (int)*(uint32_t *)&buf4[0]/10;

    break;
  case 5:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    V_BN = (int)*(uint32_t *)&buf4[0]*10;
    break;
  case 6:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    V_CN = (int)*(uint32_t *)&buf4[0]*10;
    break;
  case 7:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    A_A = (int)*(uint32_t *)&buf4[0] /10;
    break;
  case 8:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];  
    A_B = (int)*(uint32_t *)&buf4[0] /10;
    break;
  case 9:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    A_C = (int)*(uint32_t *)&buf4[0] /10;
    break;
  case 10:
    buf4[0] = p_data[4];
    buf4[1] = p_data[3];
    buf4[2] = p_data[6];
    buf4[3] = p_data[5];
    kvar = (int)*(uint32_t *)&buf4[0] /10;
    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}