#ifndef __TEMPNODETCS30A22_HPP__
#define __TEMPNODETCS30A22_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class tempNodeTcs30a22 : public analogNode
{
public:
    tempNodeTcs30a22();
    virtual ~tempNodeTcs30a22();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void updateMessage();
  void sendMessageToBa();
// private:
  int m_id;
  int m_dio_type;
  int m_decimal_value_alarm;
  int m_decimal_value_alarmdir;
  int m_temperature_alarmdir;
  int m_temperature_alarm;
  int m_decimal_value_alarm_threshold;
  float m_temperature_alarm_threshold;
  bool m_decimal_value_alarm_triggered = false;
  bool m_temperature_alarm_triggered = false;
  
  int status = 1;
  int last_update_timestamp;
  float m_temp;
  float m_decimal_value;

  uint8_t m_buf[10];
};

#endif
