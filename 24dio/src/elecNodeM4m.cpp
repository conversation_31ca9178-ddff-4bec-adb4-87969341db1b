#include <ctime>
#include "elecNodeM4m.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeM4m::elecNodeM4m()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeM4m::~elecNodeM4m()
{
}

int elecNodeM4m::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  // 根據address.md中的位址定義
  int reg_arr[] =
      {
          20488,  // Active energy - net (KWH) 4 0.01kwh Signed
          23330,  // var 0.01	Signed
          23304,  // Line voltage L1-L2 (V) 2 0.1v Unsigned
          23306,  // Line voltage L3-L2 (V) 2 0.1v Unsigned
          23308,  // Line voltage L1-L3 (V) 2 0.1v Unsigned
          23310,  // Three phase system current (A) 2 0.01a Unsigned
          23312,  // Current L1 (A) 2 0.01a Unsigned
          23314,  // Current L2 (A) 2 0.01a Unsigned
          23316,  // Current L3 (A) 2 0.01a Unsigned
          23322,  // Active power (W) Total 2 0.01w Signed
          23346,  // Frequency (Hz) 1 0.01hz Unsigned
          23360   // Power factors Total 1 0.001 Signed
      };
      
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    
    // 根據address.md中的SIZE欄位設定讀取長度
    if (i == 0 || i == 1) {
      send[5] = 4; // Active energy和Reactive energy的SIZE為4
    } else if (i == 10 || i == 11) {
      send[5] = 1; // Frequency和Power factors的SIZE為1
    } else {
      send[5] = 2; // 其他參數的SIZE為2
    }

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeM4m::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  
  // 計算回應的長度
  int aspect_length = 3; // 固定部分: 地址(1) + 功能碼(1) + 數據長度(1)
  
  // 根據不同的索引計算不同的回應長度
  if (m_index == 0 || m_index == 1) {
    aspect_length += 8; // Active energy和Reactive energy的SIZE為4，數據長度為8
  } else if (m_index == 10 || m_index == 11) {
    aspect_length += 2; // Frequency和Power factors的SIZE為1，數據長度為2
  } else {
    aspect_length += 4; // 其他參數的SIZE為2，數據長度為4
  }
  
  // 驗證CRC
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }

    cout << "M4m response 83 error" << endl;
    m_index = 0;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
    }

    cout << "M4m crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    m_index = 0;
    return true;
  }
  
  // 處理數據
  uint8_t buf[8]; // 使用8字節緩衝區以支持最大的數據類型
  int value;

  switch (m_index)
  {
  case 0: // Active energy - net (KWH)
    // 處理4字節數據
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    buf[4] = p_data[6];
    buf[5] = p_data[5];
    buf[6] = p_data[4];
    buf[7] = p_data[3];
    kwh = (*(int64_t *)&buf[0]) ; // 0.01 resolution
    break;
    
  case 1: // Reactive energy - net (kvarh)
    // 處理4字節數據
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    // buf[4] = p_data[6];
    // buf[5] = p_data[5];
    // buf[6] = p_data[4];
    // buf[7] = p_data[3];
    kvar = (*(int32_t *)&buf[0]) *0.001 ; // 0.01 resolution
    break;
    
  case 2: // Line voltage L1-L2 (V)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    volage = (*(uint16_t *)&buf[0]) * 10; // 0.1 resolution
    break;
    
  case 3: // Line voltage L3-L2 (V)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    V_BN = (*(uint16_t *)&buf[0]) * 10; // 0.1 resolution
    break;
    
  case 4: // Line voltage L1-L3 (V)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    V_CN = (*(uint16_t *)&buf[0]) * 10; // 0.1 resolution
    break;
    
  case 5: // Three phase system current (A)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    current = (*(uint16_t *)&buf[0]) ; // 0.01 resolution
    break;
    
  case 6: // Current L1 (A)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_A = (*(uint16_t *)&buf[0]) ; // 0.01 resolution
    break;
    
  case 7: // Current L2 (A)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_B = (*(uint16_t *)&buf[0]) ; // 0.01 resolution
    break;
    
  case 8: // Current L3 (A)
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_C = (*(uint16_t *)&buf[0])  ; // 0.01 resolution
    break;
    
  case 9: // Active power (W) Total
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    // buf[4] = p_data[10];
    // buf[5] = p_data[9];
    // buf[6] = p_data[8];
    // buf[7] = p_data[7];
    kw = (*(int32_t *)&buf[0]) * 0.001; // 0.01 resolution, convert to kW
    break;
    
  case 10: // Frequency (Hz)
    buf[0] = p_data[4];
    buf[1] = p_data[3];
    freq = (*(uint16_t *)&buf[0]) ; // 0.01 resolution
    break;
    
  case 11: // Power factors Total
    buf[0] = p_data[4];
    buf[1] = p_data[3];
    power_factor = (*(int16_t *)&buf[0]) /10 ; // 0.001 resolution
    updateMessage(); // 最後一個參數處理完後更新訊息
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}
