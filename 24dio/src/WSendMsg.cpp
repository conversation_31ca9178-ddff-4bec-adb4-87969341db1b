#include "WSendMsg.hpp"
#include "utility.hpp"
#include "baUtil.hpp"

#include <curl/curl.h>
#include <curl/easy.h>
#include <algorithm>
WSendMsg::WSendMsg()
{
    this->is_local = false;
    this->is_remote = true;
    this->retry = MAX_SEND_RETRY;
    this->post.empty();
}
WSendMsg::~WSendMsg()
{

}
void WSendMsg::set(string cmd,string log,string LOG,bool is_json,bool is_show)
{
    this->cmd = cmd;
    this->log = log;
    this->LOG = LOG;
    this->is_json = is_json;
    this->is_show = is_show;

}

bool WSendMsg::resend()
{
  if(--retry == 0)    return true;

  return do_send();

}
void WSendMsg::set_remote(bool val)
{
  is_remote = val;
}

void WSendMsg::set_local(bool val)
{
  is_local = val;
}
void WSendMsg::setJson(std::string json)
{
  this->json = json;
}
void WSendMsg::send_to_marco_service(std::string body)
{
  string cmd_1 = body;
  cmd_1.erase(std::remove(cmd_1.begin(), cmd_1.end(), '\"'), cmd_1.end());
  stringstream ss1;
  ss1 << "curl --header \"Content-Type: application/json\" ";
  ss1 << "--request POST ";
  ss1 << "--data '" << "{ \"body\": \"" << cmd_1 <<" \"}' " ;
  // 移除不必要的檔案輸出，減少 IO 負擔
  ss1 << "http://"<<"127.0.0.1/rp/api/httpcall"<<" -k --connect-timeout 3";
  ss1 << " > /dev/null 2> /dev/null &";
  system(ss1.str().c_str());
    // cout << "do_send1" << ss1.str().c_str() << endl ;
}
bool WSendMsg::do_send()
{
  vector<Host>& hosts = baUtil::getHosts();
  send_to_marco_service(cmd);
  for(auto& host : hosts)
  {
    if(is_remote && host.isBaType())
    {

      do_send_ipaddr(host.getIpaddr());
    }
  }

  if(is_local)
  {
    do_send_ipaddr("127.0.0.1");

  }

  return true;
}

static size_t WriteCallback(void *contents, size_t size, size_t nmemb, void *userp)
{
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

bool WSendMsg::curl_send(string ipaddr)
{
  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<ipaddr<<endl;
  //cout<<ipaddr+cmd<<endl;
  //cout<<post<<endl;


  char error[CURL_ERROR_SIZE]={0};

  auto curl = curl_easy_init();        //初始化一個CURL型別的指標
  string str_file;
  ipaddr = string("http://")+ipaddr+cmd;
  if(curl!=NULL)
  {


      //設定curl選項. 其中CURLOPT_URL是讓使用者指定url. argv[1]中存放的命令列傳進來的網址
      curl_easy_setopt(curl, CURLOPT_URL,ipaddr.c_str() );
      curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post.c_str());

      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
      curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, error);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, &str_file);
      //curl_easy_setopt(curl, CURLOPT_POST, 1);
      curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3);

      //呼叫curl_easy_perform 執行我們的設定.並進行相關的操作. 在這裡只在螢幕上顯示出來.
      auto res = curl_easy_perform(curl);
      //清除curl操作.
      curl_easy_cleanup(curl);
      curl_global_cleanup();

      //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<ipaddr<<" "<<log<<" "<<LOG<<" "<<res<<" "<<CURLE_OK<<endl;

      if(res != CURLE_OK)
      {
        string str_error;

        str_error = post+" "+error;
        //cout<<"\n"<<str_error<<endl;
        //cout<<"\n"<<log<<endl;
        writeToFile(log,str_error.c_str());

      }
      else
      {
        //cout<<"\n"<<str_file<<endl;
        //cout<<"\n"<<post<<endl;
        writeToFile(LOG,str_file.c_str());

        writeToFile(log,post.c_str());

      }
  }

  return true;

}
void WSendMsg::set_post(string post)
{
    this->post = post;
}
bool WSendMsg::do_send_ipaddr(string ipaddr)
{
  std::stringstream ss;

  string LOG_ipaddr = LOG+ipaddr;

  if(is_json)
  {
    ss << "curl --header \"Content-Type: application/json\" ";
    ss << "--request POST ";
    ss << "--data '" << this->json<<"' " ;
    // 移除不必要的檔案輸出，減少 IO 負擔
    ss << "https://"<<ipaddr<<cmd<<" -k --connect-timeout 3";
    ss << " > /dev/null 2> /dev/null &";

    system(ss.str().c_str());
    //cout<<ss.str()<<endl;
  }
  else
  {
      // 移除不必要的檔案輸出，減少 IO 負擔
      ss << "curl https://"<<ipaddr<<cmd<<"  -k --connect-timeout 3 > /dev/null 2> /dev/null &";

      if(post.size())
      {

          curl_send(ipaddr);
      }
      else
          system(ss.str().c_str());
  }

  if(is_show)
  {
      std::cout << getNowTime();
      std::cout<<ss.str()<<endl;
      fflush(stdout);
  }
  return true;
}

bool WSendMsg::do_send(string json)
{
  vector<Host>& hosts = baUtil::getHosts();
  // cout << "do_send~~~~~~~~~~~~~~~~~~~~~~~~~~~~~" << endl; 
  for(auto& host : hosts)
  {
    if(is_remote && host.isBaType())
    {
      do_send(json,host.getIpaddr());
    }
  }

  if(is_local)
  {
    do_send(json,"127.0.0.1");
  }

}

bool WSendMsg::do_send(string json,string ipaddr)
{
    std::stringstream ss;

    std::cout << getNowTime();

    // 移除不必要的檔案輸出，減少 IO 負擔
    ss << "curl -X POST https://"<<ipaddr<<"/index.php?option=\"com_floor&task=sroots.update_ip2\""<<" -d "<<"'"<<json<<"'" <<" -k --connect-timeout 3 > /dev/null 2> /dev/null &";
    system(ss.str().c_str());
    std::cout<<ss.str()<<endl;
    fflush(stdout);

}
