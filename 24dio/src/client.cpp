#include <iostream>
#include <string>

#include <sys/socket.h>
#include <thread>

#include "wdef.h"
#include "client.h"
#include "utility.hpp"
//#include <memory> // shared_ptr
#include "baUtil.hpp"
#include "anotherLoop.hpp"
#include "centerLoop.hpp"
#include "rs485Loop.hpp"

#define SOCKET_NAME "/tmp/diosocket2"
#define DOOR_PORT 8031

using namespace std;

static int init_socket();
static void idle_loop();
static void lookup_event(int fd);
static bool is_main_fd(int fd);
 
static void process_sockfd(int fd,fd_set *p_fd_r,fd_set *p_fd_e);

static void process_door_sockfd(int fd,fd_set *p_fd_r,fd_set *p_fd_e);
static void accept_sockfd(fd_set* p_fd_r,fd_set* p_fd_e);
static void accept_door_sockfd(fd_set* p_fd_r,fd_set* p_fd_e);


static int is_packet(string str);

static void remove_fd(int fd);
static bool is_door_fd(int fd);

static map<int, string> mapRecv;

static int m_server_sockfd;
static int m_door_sockfd;
static struct sockaddr_un m_server_address;
static int m_server_len;
static struct sockaddr_un m_door_address;
static int m_door_len;

static int m_maxfd;

static fd_set m_fd_r;    /* set of fifo read filedescriptors for select() */
static fd_set m_fd_e;

static bool m_run;

static vec_int m_fds;


int init_door_socket()
{
     int  listenfd, connfd;
     struct sockaddr_in  servaddr;

     int  n;

     if( (m_door_sockfd = socket(AF_INET, SOCK_STREAM, 0)) == -1 ){
         printf("create socket error: %s(errno: %d)\n",strerror(errno),errno);
         return 0;
     }

     memset(&servaddr, 0, sizeof(servaddr));
     servaddr.sin_family = AF_INET;
     servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
     servaddr.sin_port = htons(DOOR_PORT);

     if( bind(m_door_sockfd, (struct sockaddr*)&servaddr, sizeof(servaddr)) == -1){
         printf("bind socket error: %s(errno: %d)\n",strerror(errno),errno);
         return 0;
     }

     if( listen(m_door_sockfd, 10) == -1){
         printf("listen socket error: %s(errno: %d)\n",strerror(errno),errno);
         return 0;
     }

    cout <<"\n"<<__func__<<" "<<m_door_sockfd<<endl;
     return m_door_sockfd;
}

int init_socket()
{
/*  Remove any old socket and create an unnamed socket for the server.  */

    printf("\n%s %d",__func__,__LINE__);
    unlink(SOCKET_NAME);
    m_server_sockfd = socket(AF_UNIX, SOCK_STREAM, 0);

/*  Name the socket.  */

    m_server_address.sun_family = AF_UNIX;
    strcpy(m_server_address.sun_path, SOCKET_NAME);
    m_server_len = sizeof(m_server_address);
    bind(m_server_sockfd, (struct sockaddr *)&m_server_address, m_server_len);

    stringstream ss;
    ss<< "chmod o+w "<< SOCKET_NAME;

    system(ss.str().c_str());
/*  Create a connection queue and wait for clients.  */

    listen(m_server_sockfd,10);

    /* prepare fdset for select */

    printf("\n%s %d %d %d ",__func__,__LINE__,m_server_sockfd,m_maxfd);
    fflush(stdout);
    return 0;
}
void clearFdToSystem(int fd,bool is_rs485)
{
  if(is_rs485)
  {
    rs485_clearFdToSystem(fd);
    return;
  }
  FD_CLR(fd, &m_fd_r);
  FD_CLR(fd, &m_fd_e);

  if(fd == m_maxfd)
  {
      m_maxfd--;
  }

  cout<<__func__<<" "<<fd<<" "<<m_maxfd<<endl;
  fflush(stdout);
}
void addFdToSystem(int fd,bool is_rs485)
{
  if(is_rs485)
  {
    rs485_addFdToSystem(fd);
    return;
  }
  FD_SET(fd, &m_fd_r);
  FD_SET(fd, &m_fd_e);

  m_maxfd = max<int>(m_maxfd, fd);
  cout<<__func__<<" "<<fd<<" "<<m_maxfd<<endl;
  fflush(stdout);

}

void idle_loop()
{
     //int ret;
     FD_ZERO(&m_fd_r);
     FD_ZERO(&m_fd_e);

     m_maxfd = 0;

     FD_SET(m_server_sockfd, &m_fd_r);
     FD_SET(m_server_sockfd, &m_fd_e);

     FD_SET(m_door_sockfd, &m_fd_r);
     FD_SET(m_door_sockfd, &m_fd_e);

     m_maxfd = max<int>(m_maxfd, m_server_sockfd);

     m_maxfd = max<int>(m_maxfd, m_door_sockfd);

     struct timeval timeout;                // timeout for select()
     timeout.tv_usec = 500000;
     timeout.tv_sec = 0;
     time_t seconds;

     seconds = 0;
     m_run = true;
     time_t reportAliveTime = 0;

     while(m_run)
     {
       

          time_t tm = time(0);

          if (difftime(tm, reportAliveTime) > 600)
          {
            stringstream ss1;
            ss1 << "curl ";              
            ss1 << "http://"<<"127.0.0.1/rp/api/alive"<<" -k --connect-timeout 5 -o "<< "127.0.0.1:5888" ;
            ss1 << " > /dev/null 2> /dev/null &";     
            system(ss1.str().c_str());
            reportAliveTime = tm;
          }



          int pass_seconds = difftime(tm,seconds);

          if(pass_seconds)
          {
            main_timeout(pass_seconds,false);
            seconds = tm;

          }

           fd_set testfds_r;
           fd_set testfds_e;
          int ret;
          //printf("\n%s ",__func__);
          //timeout.tv_usec = 40000;

           testfds_r = m_fd_r;
           testfds_e = m_fd_e;
           ret = select(m_maxfd+1, &testfds_r, NULL, &testfds_e, &timeout);
           //printf("\n%s %d ",__func__,ret);
           if (ret < 0) {
    	         //fprintf(stdout, "select()'s error was %s", strerror(ret));
               //cout << " select() client"<<endl;
               //fflush(stdout);
    	         //exit(EXIT_FAILURE);
               //m_run = false;
               continue;
            }

            if(ret == 0)
            {
                timeout.tv_usec = 500000;
                timeout.tv_sec = 0;
                continue;
            }
            else
            {
                int fd;
                for(fd=0;fd<m_maxfd+1;fd++)
                {
                    if (FD_ISSET(fd, &testfds_r))
                    {
                        lookup_event(fd);
                    }

                    if (FD_ISSET(fd, &testfds_e))
                    {
                        fprintf(stderr, "fd %d error",fd);

                    }

               }

             }

             if (FD_ISSET(m_server_sockfd, &testfds_r))
             {
                 accept_sockfd(&m_fd_r,&m_fd_e);
             }

             if (FD_ISSET(m_server_sockfd, &testfds_e))
             {
                 fprintf(stderr, "m_server_socket error");

             }

             if (FD_ISSET(m_door_sockfd, &testfds_r))
             {
                 accept_door_sockfd(&m_fd_r,&m_fd_e);
             }

             if (FD_ISSET(m_door_sockfd, &testfds_e))
             {
                 fprintf(stderr, "m_door_socket error");

             }

      }
}

bool is_main_fd(int fd)
{
  bool ret;

  ret = false;
  if(fd == m_server_sockfd)
      ret = true;

  else if(fd == m_door_sockfd)
      ret = true;

  return ret;
}

bool is_door_fd(int fd)
{
  bool ret;

  ret = false;
  for(vec_int::iterator iter=m_fds.begin(); iter!=m_fds.end(); iter++)
  {
      if( *iter == fd)
      {
             ret = true;
             break;
      }
  }

  return ret;

}
void lookup_event(int fd)
{
  if(baUtil::find_device_and_process(fd,false) == false && !is_main_fd(fd))
  {
       if(is_door_fd(fd))
           process_door_sockfd(fd,&m_fd_r,&m_fd_e);
       else
           process_sockfd(fd,&m_fd_r,&m_fd_e);
  }

}

void doClient(int idle)
{
  thread mCenterThread(centerLoop,idle);
  thread mRS485Thread(rs485Loop,idle);

  init_socket();
  init_door_socket();
  m_run = true;
  while(m_run)
  {
    idle_loop();
  }

}
#include <unistd.h>
#include <signal.h>
void terminateHandler(int sig)
{
    m_run = false;
    printf("sig = %d\n", sig);
}
void sigpipeHandler(int sig)
{
  cout << "sigpipeHandler:" << sig << endl;
}
int client()
{
  signal(SIGINT, terminateHandler);
  signal(SIGTERM, terminateHandler);
  signal(SIGPIPE, sigpipeHandler);
  if(baUtil::isBaRole())
  {
    cout<<"\nbaUtil::isBaRole"<<endl;
    fflush(stdout);
    doClient(BA_ROLE);

    return 0;
  }
  else
  {
    int rank;
    baUtil::isCenterRole(&rank);

    cout<<"\nbaUtil::isCenterRole "<<rank<<endl;
    fflush(stdout);
    if(rank > 1 )
    {
        string ipaddr;
        ipaddr = baUtil::findFirstCenter();
        if(baUtil::isFirstAlive())
        {
          cout<<"\nbaUtil::isFirstAlive"<<endl;
          fflush(stdout);
          system("supervisorctl stop pjsip");
          doClient(RELAY_ROLE);
          return 0;
        }
    }

  }

  system("supervisorctl restart pjsip");

  set_normal_run();
  baUtil::getBaData();

  thread mThread(anotherLoop);

  doClient(NORMAL_RUN);

  return 0;

}


void accept_sockfd(fd_set* p_fd_r,fd_set* p_fd_e)
{
    int fd;
    unsigned int len;

    //cout<<__func__<<endl;
    //fflush(stdout);
    len = sizeof(m_server_address);
    fd = accept(m_server_sockfd,
          (struct sockaddr *)&m_server_address, &len);


    FD_SET(fd, p_fd_r);
    FD_SET(fd, p_fd_e);

    m_maxfd = max<int>(m_maxfd, fd);

    //printf("\n%s %d %d ",__func__,__LINE__,fd);
    //fflush(stdout);

}

void accept_door_sockfd(fd_set* p_fd_r,fd_set* p_fd_e)
{
    int fd;
    unsigned int len;

    //cout<<__func__<<endl;
    //fflush(stdout);
    len = sizeof(m_door_address);
    fd = accept(m_door_sockfd,
          (struct sockaddr *)&m_door_address, &len);


    FD_SET(fd, p_fd_r);
    FD_SET(fd, p_fd_e);

    m_fds.push_back(fd);
    m_maxfd = max<int>(m_maxfd, fd);

    //printf("\n%s %d %d ",__func__,__LINE__,fd);
    //fflush(stdout);

}

int is_packet(string str)
{
    char buf[9];
    int i;
    int len;
    int sum;

    len = str.size();

    for(i=0;i<8;i++)
    {
      buf[i] = str[3+i];
    }

    buf[8] = 0;

    sum = strtol(buf, NULL, 16);

    if((sum+11) <= len)
    {
      return 1;
    }

    return 0;

}

void remove_fd(int fd)
{
  for(vec_int::iterator iter=m_fds.begin(); iter!=m_fds.end(); iter++)
  {
      if( *iter == fd)
      {
             m_fds.erase(iter);
             break;
      }
  }
}
void process_sockfd(int fd,fd_set *p_fd_r,fd_set *p_fd_e)
{

  int nread;

  ioctl(fd,FIONREAD,&nread);

  if(nread == 0)
  {
      FD_CLR(fd, p_fd_r);
      FD_CLR(fd, p_fd_e);

      mapRecv[fd] = "";

      if(fd == m_maxfd)
      {
        m_maxfd--;
      }

      close(fd);


      //cout  << "close  " << __func__;
      //fflush(stdout);

  }
  else
  {
       int res;

       //cout << " " << sizeof(int) << " "<< vec.size() << endl;
       char data[300+1];
       const char *p_buf;

       int len = min<int>(300,nread);
       res = read(fd, data, len);
       data[res] = 0;

       if(strncmp(data,"test",4) == 0)
       {
         write(fd,"OK",2);
         cout <<".";
         fflush(stdout);
         return;
       }

      //cout <<data<<endl;
      if(strncmp(data,"WA=",3) == 0)
      {
        mapRecv[fd] = data;
        if(is_packet(mapRecv[fd]) == 1)
        {
          p_buf = mapRecv[fd].c_str();
          p_buf = &p_buf[3+8];
        }
        else
        {
          return;
        }
      }
      else {
        map<int, string>::iterator iter;

        iter = mapRecv.find(fd);

        if(iter != mapRecv.end())
        {
            mapRecv[fd] = mapRecv[fd]+data;
            if(is_packet(mapRecv[fd]) == 1)
            {
              p_buf = mapRecv[fd].c_str();
              p_buf = &p_buf[3+8];
            }
            else
            {
              return;
            }
        }
      }

       vec_string mydatas =  splitter('\n', p_buf);
       for(unsigned int i=0;i<mydatas.size();i++)
       {
           process_data(mydatas[i]);
           //sleep(1);

       }

  }
}

void process_door_sockfd(int fd,fd_set *p_fd_r,fd_set *p_fd_e)
{

  int nread;

  ioctl(fd,FIONREAD,&nread);

  if(nread == 0)
  {
      FD_CLR(fd, p_fd_r);
      FD_CLR(fd, p_fd_e);

      if(fd == m_maxfd)
      {
        m_maxfd--;
      }

      close(fd);
      remove_fd(fd);

      //cout  << "close  " << __func__;
      //fflush(stdout);

  }
  else
  {

    char data[300+1];

    int len = min<int>(300,nread);

    int res = read(fd, data, len);

    data[res] = 0;

    cout <<"\n"<< data<<endl;

    if(res < 43)  return;

    if(is_valid_time(data))
        baUtil::doorCardEvent(data);
  }
}

void exit_process()
{
  cout<<__func__<<endl;
  fflush(stdout);
  m_run = false;
}
