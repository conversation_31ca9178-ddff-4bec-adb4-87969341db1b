#ifndef __ELECNODEKTMK3_HPP__
#define __ELECNODEKTMK3_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"

/**
 * KT-MK3 電表節點類別
 * 支援 Modbus RTU OverTCP 協定，使用 Input Register (32bit sw. float) 資料格式
 * 功能碼：0x04 (讀取 Input Register)
 */
class elecNodeKtMk3 : public elecNode
{
public:
  elecNodeKtMk3();
  virtual ~elecNodeKtMk3();

  /**
   * 設定電表參數並建立 Modbus 指令
   * @param value JSON 設定值
   * @return 成功返回 1
   */
  int set(Json::Value value);
  
  /**
   * 處理從電表接收到的資料
   * @param p_data 接收到的資料緩衝區
   * @param len 資料長度
   * @return 處理成功返回 true
   */
  bool set_data(uint8_t *p_data, int len);
  
  /**
   * 設定累積電能值
   * @param kwh 累積電能值
   */
  void setKWH(long kwh);
  
  /**
   * 取得累積電能值
   * @return 累積電能值
   */
  long getKWH();

private:
  // KT-MK3 電表測量項目索引
  enum KtMk3Index {
    VAB_INDEX = 0,    // Vab 線電壓 AB
    VBC_INDEX = 1,    // Vbc 線電壓 BC  
    VCA_INDEX = 2,    // Vca 線電壓 CA
    IA_INDEX = 3,     // Ia A相電流
    IB_INDEX = 4,     // Ib B相電流
    IC_INDEX = 5,     // Ic C相電流
    I_INDEX = 6,      // I 總電流
    KVAR_INDEX = 7,   // Kvar 無功功率
    PF_INDEX = 8,     // pf 功率因數
    FREQ_INDEX = 9,   // freq 頻率
    KWH_INDEX = 10,   // kwh 累積電能
    KW_INDEX = 11     // kw 有功功率
  };
};

#endif
