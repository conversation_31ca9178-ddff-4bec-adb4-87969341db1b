project(24dio)

set(JSON_INC_DIR "/usr/include/jsoncpp/")
include_directories(BEFORE ${JSON_INC_DIR})

add_compile_options(-std=c++11)

add_library(MyLibrary ./src/crc.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/TimingSet.cpp)
add_library(MyLibrary ./src/client.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/dio24Dev.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/digitNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/analogNode.cpp)  # 建立程式庫 MyLibrary


add_library(MyLibrary ./src/irtiIvaPersonDetectionNode.cpp)
add_library(MyLibrary ./src/irtiIvaPersonCountingNode.cpp)
add_library(MyLibrary ./src/elecNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/elecNodeCic.cpp)
add_library(MyLibrary ./src/elecNodeOpcDa.cpp)
add_library(MyLibrary ./src/elecNodeOpcDaV2.cpp)
add_library(MyLibrary ./src/elecNodeAemDrb.cpp)
add_library(MyLibrary ./src/elecNodeTatung.cpp)
add_library(MyLibrary ./src/elecNodeShihlin.cpp)
add_library(MyLibrary ./src/elecNodeWeema1p.cpp)
add_library(MyLibrary ./src/elecNodeWeema3p.cpp)
add_library(MyLibrary ./src/elecNodeCicBaw1a2a.cpp)
add_library(MyLibrary ./src/elecNodeCicBaw2c.cpp)
add_library(MyLibrary ./src/elecNodeAcuvim.cpp)


add_library(MyLibrary ./src/elecNodeBenderPem333.cpp)
add_library(MyLibrary ./src/elecNodeBenderPem575.cpp)
add_library(MyLibrary ./src/elecNodePrimevolt.cpp)
add_library(MyLibrary ./src/elecNodeShihlinSPM8Solar.cpp)
add_library(MyLibrary ./src/elecNodeGeneralSolar.cpp)
add_library(MyLibrary ./src/elecNodeVmrMp7.cpp)
add_library(MyLibrary ./src/elecNodeVmrMp8.cpp)
add_library(MyLibrary ./src/elecNodeDaePm210.cpp)
add_library(MyLibrary ./src/elecNodeM4m.cpp)
add_library(MyLibrary ./src/elecNodeKtMk3.cpp)
add_library(MyLibrary ./src/elecNodeBaw4c.cpp)
add_library(MyLibrary ./src/fujiElevatorDev.cpp)


add_library(MyLibrary ./src/tempNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/generalAiNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodePinron.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/weemaIaqNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeYonGjia.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeYonGjiaWithLux.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeYonGjia2.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeYonGjia3In1.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeYonGjia3In1Co.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeTcs30a22.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeTcs5282.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/coSensorNodeYonGjia.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/pmSensorNodeYonGjia.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/tempNodeJnc.cpp)

add_library(MyLibrary ./src/coSensorNode.cpp)
add_library(MyLibrary ./src/coSensorNodeGeneral.cpp)
add_library(MyLibrary ./src/co2SensorNode.cpp)
add_library(MyLibrary ./src/jetecWindDirectionNode.cpp)
add_library(MyLibrary ./src/jetecRainMeterNode.cpp)
add_library(MyLibrary ./src/jetecSoilMeterNode.cpp)
add_library(MyLibrary ./src/waterMeterNode.cpp)
add_library(MyLibrary ./src/waterMeterNodeTkd.cpp)
add_library(MyLibrary ./src/waterMeterNodeOpcDaV2.cpp)
add_library(MyLibrary ./src/waterMeterNodeCc130.cpp)
add_library(MyLibrary ./src/mitsubishiElevatorDev.cpp)
add_library(MyLibrary ./src/liuchuanElevatorDev.cpp)
add_library(MyLibrary ./src/yunyangFireFightingDev.cpp)
add_library(MyLibrary ./src/baochungFireFightingDev.cpp)
add_library(MyLibrary ./src/smartLampNode.cpp)
add_library(MyLibrary ./src/RS485Msg.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/modbusTCP.cpp)  # 建立程式庫 MyLibrary
#add_library(MyLibrary ./src/modbusRS485Dev.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/ADevice.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ANode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/utility.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/modbusSoyal.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ElecMain.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/TopAlarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/Alarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/AlarmNode.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/AAlarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/AAlarmNode.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/TopDoorAlarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/DoorAlarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/DoorAlarmNode.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/TopCondition.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/Condition.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/modbusDev.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/modbusNode.cpp)  # 建立程式庫 MyLibrary
#add_library(MyLibrary ./src/dioNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/dioJob.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/WSendMsg.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/msgCallee.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/myAccount.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/TopDoorAlarm.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/Ba.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/baUtil.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ipNode.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ipDev.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/anotherLoop.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/rs485Loop.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/Host.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/centerLoop.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/AEvent.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/phoneDev.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/phoneNode.cpp)  # 建立程式庫 MyLibrary

add_library(MyLibrary ./src/TopDoorCard.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ADoorCard.cpp)  # 建立程式庫 MyLibrary
add_library(MyLibrary ./src/ADoorCardNode.cpp)  # 建立程式庫 MyLibrary

add_library(MyTestLibrary ./src/BaTest.cpp)  # 建立程式庫 MyLibrary

add_executable(test-app ./src/client3.cpp ./src/crc.cpp) # 建立執行檔 MyProgram
add_executable(24dio-app ./src/main.cpp) # 建立執行檔 MyProgram
add_executable(json ./src/json.cpp) # 建立執行檔 MyProgram
add_executable(mygtest ./src/gtest.cpp) # 建立執行檔 MyProgram

add_executable(ba ./ba.c) # 建立執行檔 MyProgram

# 產生執行檔 MyProgram 與 程式庫 MyLibrary 連結指令
target_link_libraries(24dio-app MyLibrary pthread jsoncpp curl)
target_link_libraries(json jsoncpp)
target_link_libraries(mygtest gtest MyLibrary MyTestLibrary jsoncpp pthread curl)
target_link_libraries(ba pthread)
